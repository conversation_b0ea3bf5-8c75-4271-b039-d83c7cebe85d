{"name": "apexiqedtech", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@radix-ui/react-collapsible": "^1.1.4", "@radix-ui/react-radio-group": "^1.2.4", "@radix-ui/react-select": "^2.2.2", "@radix-ui/react-slot": "^1.1.2", "@tanstack/react-query": "^5.71.1", "@uiw/react-md-editor": "^4.0.7", "apexiqedtech": "file:", "class-variance-authority": "^0.7.1", "date-fns": "^4.1.0", "framer-motion": "^12.6.2", "icons": "^1.0.0", "js-cookie": "^3.0.5", "lucide-react": "^0.486.0", "next": "15.2.4", "react": "^18.3.1", "react-dom": "^18.3.1", "react-hook-form": "^7.55.0", "react-hot-toast": "^2.5.2", "react-icons": "^5.5.0", "react-pdf": "^9.2.1", "react-phone-input-2": "^2.15.1", "react-phone-number-input": "^3.4.12", "react-quill": "^2.0.0", "react-slick": "^0.30.3", "recharts": "^2.15.3", "slick-carousel": "^1.8.1", "swiper": "^11.2.6", "tailwind-merge": "^3.2.0", "zustand": "^5.0.3"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/date-fns": "^2.5.3", "@types/js-cookie": "^3.0.6", "@types/node": "^20", "@types/react": "^19.0.12", "@types/react-dom": "^19.0.4", "@types/react-slick": "^0.23.13", "@types/recharts": "^1.8.29", "eslint": "^9", "eslint-config-next": "15.2.4", "tailwindcss": "^4", "typescript": "^5"}}