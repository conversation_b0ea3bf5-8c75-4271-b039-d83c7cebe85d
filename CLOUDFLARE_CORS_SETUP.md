# Cloudflare R2 CORS Configuration Guide

## Problem
You're getting CORS errors when uploading files to Cloudflare R2 from your frontend application:

```
Access to fetch at 'https://34ed7b79aa5111e15c7da97693e05916.r2.cloudflarestorage.com/edutech/...' 
from origin 'http://localhost:3000' has been blocked by CORS policy: 
No 'Access-Control-Allow-Origin' header is present on the requested resource.
```

## Solution: Configure CORS in Cloudflare R2

### Step 1: Access Cloudflare Dashboard
1. Go to [Cloudflare Dashboard](https://dash.cloudflare.com/)
2. Navigate to **R2 Object Storage**
3. Select your bucket (ID: `34ed7b79aa5111e15c7da97693e05916`)

### Step 2: Configure CORS Policy
1. Click on your bucket name
2. Go to the **Settings** tab
3. Find the **CORS Policy** section
4. Click **Edit** or **Add CORS Policy**

### Step 3: Add CORS Configuration
Copy and paste this CORS configuration:

```json
[
  {
    "AllowedOrigins": [
      "http://localhost:3000",
      "http://localhost:3001",
      "https://yourdomain.com",
      "*"
    ],
    "AllowedMethods": [
      "GET",
      "PUT", 
      "POST",
      "DELETE",
      "HEAD"
    ],
    "AllowedHeaders": [
      "*",
      "Content-Type",
      "Authorization",
      "Cache-Control",
      "x-amz-date",
      "x-amz-content-sha256"
    ],
    "ExposeHeaders": [
      "ETag",
      "x-amz-version-id"
    ],
    "MaxAgeSeconds": 3600
  }
]
```

### Step 4: Save Configuration
1. Click **Save** or **Update**
2. Wait a few minutes for the changes to propagate

### Step 5: Test Upload
1. Go back to your application
2. Try uploading a course image
3. The CORS error should be resolved

## Important Notes

- **Replace `https://yourdomain.com`** with your actual production domain
- **Keep `http://localhost:3000`** for local development
- **The `*` wildcard** allows all origins (use with caution in production)
- **Changes may take 5-10 minutes** to propagate globally

## Verification

After configuring CORS, your course creation with image upload should work without errors. You'll see:
- ✅ Successful file upload to Cloudflare R2
- ✅ Course created with HTTPS image URL
- ✅ No more CORS errors in browser console

## Troubleshooting

If you still get CORS errors:
1. **Clear browser cache** and try again
2. **Wait 10 minutes** for changes to propagate
3. **Check the CORS configuration** is saved correctly
4. **Verify your domain** is included in AllowedOrigins

## Security Considerations

For production:
- Remove `*` from AllowedOrigins
- Only include your specific domains
- Consider restricting AllowedMethods to only what you need
- Use HTTPS domains only in production
