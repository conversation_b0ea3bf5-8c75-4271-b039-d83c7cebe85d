"use client";

import React, { useState } from 'react';
import { useCreateCourse, useGetCourses } from '@/api-services/courses/courses';
import { useUploadCourseImage, validateFile } from '@/api-services/cloudflare/cloudflare';
import { useQueryClient } from '@tanstack/react-query';
import toast from 'react-hot-toast';

interface AddCourseModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess?: () => void; // Callback for successful course creation
}

const AddCourseModal: React.FC<AddCourseModalProps> = ({ isOpen, onClose, onSuccess }) => {
  const [courseName, setCourseName] = useState('');
  const [price, setPrice] = useState('');
  const [tag, setTag] = useState('');
  const [coverImage, setCoverImage] = useState<File | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [nameError, setNameError] = useState('');
  const [uploadStatus, setUploadStatus] = useState<'idle' | 'uploading' | 'uploaded' | 'error'>('idle');
  const [uploadError, setUploadError] = useState('');

  // React Query hooks
  const queryClient = useQueryClient();
  const createCourseMutation = useCreateCourse();
  const { data: coursesResponse } = useGetCourses();

  if (!isOpen) return null;

  // Handle image file upload
  const handleFileUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      // Validate file before setting
      const validation = validateFile(file, 10); // 10MB max size
      if (!validation.valid) {
        toast.error(validation.error || 'Invalid file');
        setUploadError(validation.error || 'Invalid file');
        setUploadStatus('error');
        return;
      }

      setCoverImage(file);
      setUploadStatus('uploaded');
      setUploadError('');
      toast.success('Image selected successfully');
    }
  };

  // Handle course name change with real-time validation
  const handleCourseNameChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setCourseName(value);

    // Clear previous error
    setNameError('');

    // Check for duplicate name if value is not empty
    if (value.trim() && checkDuplicateName(value.trim())) {
      setNameError(`Course name "${value.trim()}" already exists`);
    }
  };

  // Reset form fields
  const resetForm = () => {
    setCourseName('');
    setPrice('');
    setTag('');
    setCoverImage(null);
    setIsSubmitting(false);
    setNameError('');
    setUploadStatus('idle');
    setUploadError('');
  };

  // Handle modal close
  const handleClose = () => {
    if (!isSubmitting) {
      resetForm();
      onClose();
    }
  };

  // Check if course name already exists
  const checkDuplicateName = (name: string) => {
    const existingCourses = coursesResponse?.data || [];
    return existingCourses.some(course =>
      course.name.toLowerCase().trim() === name.toLowerCase().trim()
    );
  };

  // Handle form submission
  const handleSubmit = async () => {
    // Validation
    if (!courseName.trim()) {
      toast.error('Course name is required');
      return;
    }

    // Check for duplicate course name
    if (checkDuplicateName(courseName.trim())) {
      toast.error(`Course name "${courseName.trim()}" already exists. Please choose a different name.`);
      return;
    }

    // Price validation - only validate if price is entered
    if (price.trim() && (isNaN(Number(price)) || Number(price) < 0)) {
      toast.error('Please enter a valid price');
      return;
    }

    setIsSubmitting(true);

    try {
      // Price logic: if empty, set to "0.00" (free course), otherwise use entered price
      const coursePrice = price.trim() === '' ? '0.00' : price.trim();

      const payload = {
        name: courseName.trim(),
        price: coursePrice,
        tags: tag ? [tag] : [],
        thumbnail: coverImage || undefined,
      };

      // Check authentication before making request
      const token = localStorage.getItem("token");
      if (!token) {
        toast.error('You are not logged in as admin. Please login first.');
        return;
      }

      console.log('Creating course with payload:', payload);
      const response = await createCourseMutation.mutateAsync(payload);
      console.log('Course creation response:', response);

      const courseType = coursePrice === '0.00' ? 'free' : 'paid';
      toast.success(`${courseType.charAt(0).toUpperCase() + courseType.slice(1)} course created and published successfully!`);

      // Invalidate and refetch courses to update the table immediately
      console.log('Invalidating courses cache after course creation...');
      queryClient.invalidateQueries({ queryKey: ["courses"] });
      console.log('Courses cache invalidated - table will refresh automatically');

      resetForm();
      onClose();

      // Call success callback if provided
      if (onSuccess) {
        onSuccess();
      }
    } catch (error) {
      console.error('Error creating course:', error);

      // Handle specific error messages
      if (error instanceof Error) {
        if (error.message.includes('already exists')) {
          toast.error(`Course name "${courseName}" already exists. Please choose a different name.`);
        } else if (error.message.includes('Course already exists')) {
          toast.error(`A course with this name already exists. Please choose a different name.`);
        } else if (error.message.includes('CORS')) {
          toast.error('Image upload failed due to CORS policy. Please configure Cloudflare R2 CORS settings.');
          setUploadStatus('error');
          setUploadError('CORS error - Please configure Cloudflare R2 CORS settings');
        } else if (error.message.includes('Failed to upload')) {
          toast.error('Failed to upload course image. Please try again or contact support.');
          setUploadStatus('error');
          setUploadError('Image upload failed');
        } else {
          toast.error(error.message || 'Failed to create course. Please try again.');
        }
      } else {
        toast.error('Failed to create course. Please try again.');
      }
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div
      className="fixed inset-0 flex items-center justify-center bg-[#0000007d] z-[99] lg:pl-90"
      onClick={handleClose}
    >
      <div
        className="bg-[#F3F3F3] p-6  w-[746px] max-w-[90vw] max-h-[90vh] overflow-y-auto mx-4"
        onClick={(e) => e.stopPropagation()}
      >
        <div className="flex justify-between items-center mb-2">
          <h2 className="text-[17px] underline font-semibold">Add Course</h2>
          <button
            onClick={handleClose}
            disabled={isSubmitting}
            className="text-red text-[30px] disabled:opacity-50 disabled:cursor-not-allowed"
          >
            &times;
          </button>
        </div>

        {/* Modal Content */}
        <div className="space-y-4">
          {/* Course Name */}
          <div>
            <label htmlFor="courseName" className="text-sm font-medium mb-2 block">
              Course Name
            </label>
            <input
              id="courseName"
              value={courseName}
              onChange={handleCourseNameChange}
              className={`border rounded-md px-4 py-2 w-full mb-2 ${nameError ? 'border-red-500' : ''}`}
            />
            {nameError && (
              <p className="text-red-500 text-sm mb-2">{nameError}</p>
            )}
          </div>

          {/* Insert Cover Image */}
          <div>
            <label htmlFor="coverImage" className="text-sm font-medium block mb-2">
              Insert Cover Image
            </label>
            <div
              className="border  rounded-md p-6 w-full flex flex-col items-center justify-center cursor-pointer text-center"
              onClick={() => document.getElementById("coverImage")?.click()}
            >
              <input
                id="coverImage"
                type="file"
                onChange={handleFileUpload}
                className="hidden"
                accept="image/png, image/jpeg, image/jpg"
              />
              <p className="text-[11px]   mb-2"><span className='text-[#0065FF]'>Choose file </span>or Drag here</p>
              <p className="text-xs text-gray-400">Supported file types: PNG, JPG, JPEG (Max: 10MB)</p>
              {uploadStatus === 'error' && uploadError.includes('CORS') && (
                <div className="mt-2 p-2 bg-yellow-50 border border-yellow-200 rounded text-xs text-yellow-800">
                  <strong>CORS Configuration Required:</strong><br />
                  Please configure CORS policy in Cloudflare R2 dashboard to allow uploads from your domain.
                </div>
              )}
            </div>

            {coverImage && (
              <div className="mt-2 text-sm">
                <div className="flex items-center gap-2">
                  {uploadStatus === 'uploaded' && (
                    <span className="text-green-600">✓ {coverImage.name}</span>
                  )}
                  {uploadStatus === 'error' && (
                    <span className="text-red-600">✗ {coverImage.name}</span>
                  )}
                  {uploadStatus === 'uploading' && (
                    <span className="text-blue-600">⏳ Uploading {coverImage.name}...</span>
                  )}
                </div>
                {uploadError && (
                  <div className="text-red-500 text-xs mt-1">{uploadError}</div>
                )}
              </div>
            )}
          </div>

          {/* Tag and Price */}
          <div className="flex justify-between gap-4 mb-4">
            <div className="w-full">
              <label htmlFor="tag" className="text-sm font-medium mb-2 block">
                Tag (optional)
              </label>
              <select
                id="tag"
                value={tag}
                onChange={(e) => setTag(e.target.value)}
                className="border rounded-md px-4 py-2 w-full"
              >
                <option value=""></option>
                <option value="New Update">New Update</option>
                <option value="Trending">Trending</option>
                <option value="Popular">Popular</option>
                <option value="Live">Live</option>
              </select>
            </div>

            <div className="w-full">
              <label htmlFor="price" className="text-sm font-medium mb-2 block">
                Price (optional)
              </label>
              <input
                id="price"
                value={price}
                type='number'
                placeholder="Leave empty for free course"
                onChange={(e) => setPrice(e.target.value)}
                className="border rounded-md px-4 py-2 w-full"
              />
            </div>
          </div>

          {/* Create Course Button */}
          <div className="flex justify-end items-center">

            <div className="flex gap-4">
              {/* Create Course Button */}
              <button
                onClick={handleSubmit}
                disabled={isSubmitting || !!nameError || !courseName.trim()}
                className="px-4 py-2 bg-[#0A5224] text-white rounded-md text-sm disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {isSubmitting ? 'Creating...' : 'Create Course'}
              </button>


            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AddCourseModal;
