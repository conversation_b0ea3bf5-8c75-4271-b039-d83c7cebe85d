"use client";
import React, { useState ,  useEffect  } from 'react';
import Image from 'next/image';
import InputField from '@/components/common/adminCommon/InputField';

import 'react-phone-input-2/lib/style.css';
const ResetForm: React.FC = () => {
  const [formData, setFormData] = useState({
   
    email: '',
  
  

  });
 const [isFormValid, setIsFormValid] = useState(false);
  // Regex patterns
   const emailRegex = React.useMemo(() => /^[^\s@]+@[^\s@]+\.[^\s@]+$/, []);

     // Form validation
     useEffect(() => {
       const {  email } =
         formData;
   
   const isValid: boolean =
     emailRegex.test(email);
     
   setIsFormValid(isValid);
     }, [formData, emailRegex]);
  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData({ ...formData, [name]: value });
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    console.log("Form Data Submitted:", formData);
  };

  return (
    <div className=" flex flex-col items-center justify-center bg-white px-4 py-6">
      <div className="bg-[#eeeeee] rounded-md shadow-md pt-9 lg:w-[914px] md:w-[600px] h-[450px]  ">
        <div className="flex justify-center ">
          <Image
            src="/assets/images/logobig.png"
            alt="logo"
            width={130}
            height={60}
            className="h-[60px] w-auto"
          />
        </div>
        <div className='px-15'>
        <p className=" text-center justify-center  font-[Poppins] font-semibold text-[20px] md:text-[32px] leading-[110%] tracking-[-0.01em]  text-[#4b207a] mb-1">
        Forgot Password
        </p>
        </div>
        <form
  onSubmit={handleSubmit}
  className=" w-full flex flex-col items-center pt-5"
>

  <div className="w-full md:w-[75%] py-6">
    <InputField
      id="email"
      name="email"
      type="email"
      value={formData.email}
      onChange={handleChange}
      label="Enter Email"
    />
  </div>




 
  
  </form>




  <div className="w-full flex items-center justify-center relative pb-8">
            <button
              type="submit"
              disabled={!isFormValid}
              className={`py-3 px-10 ${
                isFormValid ? 'bg-[#4b207a] opacity-100 cursor-pointer ' : 'bg-[#4b207a] opacity-50 cursor-not-allowed'
              } text-white text-center font-bold text-[24px] leading-[30px] font-[Poppins] rounded-lg`}
            >
              Send Reset Code
            </button>
          </div>



      </div>
    </div>
  );
};

export default ResetForm;
