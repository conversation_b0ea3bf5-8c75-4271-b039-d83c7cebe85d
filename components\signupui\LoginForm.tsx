"use client";

import React, { useState } from "react";
import <PERSON> from "next/link";
import { Eye, EyeOff, X } from "lucide-react";
import { useRouter } from "next/navigation";
import { useStudentLogin } from "../../api-services/Login/studentLogin";

export const LoginForm = () => {
  const [showPassword, setShowPassword] = useState(false);
  const [username, setUsername] = useState("");
  const [password, setPassword] = useState("");
  const [error, setError] = useState<string | null>(null);
  const router = useRouter();
  const { mutate: login, isPending } = useStudentLogin();

  function handleSubmit(event: React.FormEvent<HTMLFormElement>): void {
    event.preventDefault();
    setError(null);
    
    login(
      { username, password },
      {
      onSuccess: (data) => {
  console.log(" user Login success data:", data);
  if (data?.status === "success") {
    console.log("Redirecting to /mycourse...");
    router.push("/mycourse");
  } else {
    setError(data?.message || "Login failed. Please try again.");
  }
}
,
        onError: (err) => {
          setError(err.message || "Login failed. Please try again.");
        },
      }
    );
  }

  return (
    <div className="bg-[#EBEBEB] p-6 lg:p-8 rounded-lg shadow-[4px_4px_4px_0px_rgba(0,0,0,0.25)] w-full relative mb-6">
      <button
        className="absolute top-4 right-4 text-gray-500 hover:text-gray-700 cursor-pointer"
        onClick={() => window.history.back()}
      >
        <X size={24} />
      </button>

      <h2 className="font-poppins text-[32px] font-semibold mb-6 text-[#4B207A]">
        Login for ApexIQ <span className="text-[#0A5224] text-sm">EduTech</span>
      </h2>

      {error && (
        <div className="mb-4 p-2 bg-red-100 text-red-700 rounded-md text-sm">
          {error}
        </div>
      )}

      <form onSubmit={handleSubmit} className="space-y-6 font-poppins">
        {/* Username Input */}
        <div className="relative">
          <input
            type="text"
            id="username"
            placeholder=" "
            value={username}
            onChange={(e) => setUsername(e.target.value)}
            required
            className="peer w-full px-4 pt-6 pb-2 border border-black rounded-md focus:outline-none focus:ring-2 focus:ring-[#0065ff]"
          />
          <label
            htmlFor="username"
            className="absolute left-4 top-2 bg-[#EBEBEB] text-[12px] text-gray-500 transition-all 
                peer-placeholder-shown:top-4 peer-placeholder-shown:text-base peer-placeholder-shown:text-gray-400
                peer-focus:top-2 peer-focus:text-[12px] peer-focus:text-[#0065ff] font-poppins"
          >
            Username or Mobile Number
          </label>
        </div>

        {/* Password Input */}
        <div className="relative">
          <input
            type={showPassword ? "text" : "password"}
            id="password"
            placeholder=" "
            value={password}
            onChange={(e) => setPassword(e.target.value)}
            required
            className="peer w-full px-4 pt-6 pb-2 border border-black rounded-md focus:outline-none focus:ring-2 focus:ring-[#0065ff]"
          />
          <label
            htmlFor="password"
            className="absolute left-4 top-2 bg-[#EBEBEB] text-[12px] text-gray-500 transition-all 
                peer-placeholder-shown:top-4 peer-placeholder-shown:text-base peer-placeholder-shown:text-gray-400
                peer-focus:top-2 peer-focus:text-[12px] peer-focus:text-[#0065ff] font-poppins"
          >
            Enter Password
          </label>
          <button
            type="button"
            onClick={() => setShowPassword(!showPassword)}
            className="absolute right-4 top-1/2 transform -translate-y-1/2 text-[#5E5E5E] cursor-pointer"
          >
            {showPassword ? <EyeOff size={20} /> : <Eye size={20} />}
          </button>
        </div>

        {/* Submit Button */}
        <button
          type="submit"
          disabled={isPending}
          className="w-full bg-[#063585] text-white font-semibold text-base py-3 rounded-lg transition-colors hover:bg-[#052a66] disabled:bg-gray-400 disabled:cursor-not-allowed"
        >
          {isPending ? "Logging in..." : "Login"}
        </button>

        {/* Forgot Password Link */}
        <div className="flex justify-end">
          <Link href="/forgotpassward">
            <span className="text-[#ff0000] text-[12px] font-poppins cursor-pointer">
              Forgot Password?
            </span>
          </Link>
        </div>
      </form>
    </div>
  );
};