// api-services/cloudflare/cloudflare.ts
import { useMutation } from "@tanstack/react-query";

// ================== CLOUDFLARE INTERFACES ==================
interface CloudflareUploadResponse {
  success: boolean;
  url: string;
  message?: string;
}

interface CloudflareUploadPayload {
  file: File;
  folder?: string; // Optional folder path like 'courses', 'modules', etc.
}

// ================== CLOUDFLARE R2 UPLOAD API ==================
const CLOUDFLARE_R2_BASE_URL = "https://34ed7b79aa5111e15c7da97693e05916.r2.cloudflarestorage.com/edutech";

async function uploadToCloudflare(payload: CloudflareUploadPayload): Promise<CloudflareUploadResponse> {
  console.log("Uploading file to Cloudflare R2:", payload.file.name);

  try {
    // Generate unique filename with timestamp
    const timestamp = Date.now();
    const fileExtension = payload.file.name.split('.').pop();
    const fileName = `${timestamp}_${payload.file.name.replace(/[^a-zA-Z0-9.-]/g, '_')}`;
    
    // Create folder path if specified
    const folderPath = payload.folder ? `${payload.folder}/` : '';
    const fullPath = `${folderPath}${fileName}`;

    // Create FormData for file upload
    const formData = new FormData();
    formData.append('file', payload.file, fileName);

    // Upload to Cloudflare R2
    const uploadUrl = `${CLOUDFLARE_R2_BASE_URL}/${fullPath}`;
    
    console.log("Uploading to URL:", uploadUrl);

    const response = await fetch(uploadUrl, {
      method: 'PUT',
      body: payload.file,
      headers: {
        'Content-Type': payload.file.type,
      },
    });

    if (!response.ok) {
      throw new Error(`Upload failed: ${response.status} ${response.statusText}`);
    }

    // Return the public URL
    const publicUrl = uploadUrl;
    
    console.log("File uploaded successfully to:", publicUrl);

    return {
      success: true,
      url: publicUrl,
      message: 'File uploaded successfully'
    };

  } catch (error) {
    console.error("Cloudflare upload error:", error);
    throw new Error(error instanceof Error ? error.message : 'Upload failed');
  }
}

// ================== SPECIFIC UPLOAD FUNCTIONS ==================

// Upload course thumbnail
async function uploadCourseImage(file: File): Promise<CloudflareUploadResponse> {
  return uploadToCloudflare({
    file,
    folder: 'courses/thumbnails'
  });
}

// Upload module overview file
async function uploadModuleFile(file: File): Promise<CloudflareUploadResponse> {
  return uploadToCloudflare({
    file,
    folder: 'modules/overviews'
  });
}

// Upload general file
async function uploadGeneralFile(file: File, folder?: string): Promise<CloudflareUploadResponse> {
  return uploadToCloudflare({
    file,
    folder: folder || 'general'
  });
}

// ================== REACT QUERY HOOKS ==================

const useUploadToCloudflare = () => {
  return useMutation<CloudflareUploadResponse, Error, CloudflareUploadPayload>({
    mutationFn: uploadToCloudflare,
    onError: (error) => {
      console.error("Cloudflare upload error:", error);
    },
  });
};

const useUploadCourseImage = () => {
  return useMutation<CloudflareUploadResponse, Error, File>({
    mutationFn: uploadCourseImage,
    onError: (error) => {
      console.error("Course image upload error:", error);
    },
  });
};

const useUploadModuleFile = () => {
  return useMutation<CloudflareUploadResponse, Error, File>({
    mutationFn: uploadModuleFile,
    onError: (error) => {
      console.error("Module file upload error:", error);
    },
  });
};

const useUploadGeneralFile = () => {
  return useMutation<CloudflareUploadResponse, Error, { file: File; folder?: string }>({
    mutationFn: ({ file, folder }) => uploadGeneralFile(file, folder),
    onError: (error) => {
      console.error("General file upload error:", error);
    },
  });
};

// ================== UTILITY FUNCTIONS ==================

// Get file type category
export const getFileCategory = (file: File): string => {
  const fileType = file.type.toLowerCase();
  
  if (fileType.startsWith('image/')) {
    return 'images';
  } else if (fileType.includes('pdf')) {
    return 'documents';
  } else if (fileType.includes('video/')) {
    return 'videos';
  } else if (file.name.endsWith('.md')) {
    return 'markdown';
  } else {
    return 'general';
  }
};

// Validate file for upload
export const validateFile = (file: File, maxSizeMB: number = 20): { valid: boolean; error?: string } => {
  // Check file size
  const maxSizeBytes = maxSizeMB * 1024 * 1024;
  if (file.size > maxSizeBytes) {
    return {
      valid: false,
      error: `File size must be less than ${maxSizeMB}MB`
    };
  }

  // Check file type for specific cases
  if (file.name.endsWith('.md')) {
    // Markdown files are allowed
    return { valid: true };
  }

  // Check for common image types
  const allowedImageTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];
  if (file.type.startsWith('image/') && !allowedImageTypes.includes(file.type)) {
    return {
      valid: false,
      error: 'Only JPEG, PNG, GIF, and WebP images are allowed'
    };
  }

  return { valid: true };
};

export {
  useUploadToCloudflare,
  useUploadCourseImage,
  useUploadModuleFile,
  useUploadGeneralFile,
  uploadToCloudflare,
  uploadCourseImage,
  uploadModuleFile,
  uploadGeneralFile
};

export type {
  CloudflareUploadResponse,
  CloudflareUploadPayload
};
