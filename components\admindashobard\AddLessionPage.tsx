"use client";

import React, { useState, useRef, useEffect } from "react";
import { useRouter } from "next/navigation";
import Image from "next/image";
import {
  FaTimes,
  FaPaperclip,
  FaBold,
  FaItalic,
  FaUnderline,
  FaStrikethrough,
  FaListUl,
  FaListOl,
  FaLink,
  FaImage,
  FaCode,
  FaQuoteLeft,
} from "react-icons/fa";
import { BsCheckCircleFill } from "react-icons/bs";

type UploadFile = {
  name: string;
  size: string;
};

const AddLessionPage = () => {
  const [lessonTitle, setLessonTitle] = useState("");
  const [projectDescription, setProjectDescription] = useState("");
  const [videoLinks, setVideoLinks] = useState<string[]>([]);
  const [videoLinkInput, setVideoLinkInput] = useState("");
  const [notesFile, setNotesFile] = useState<UploadFile[]>([]);
  const [quizFiles, setQuizFiles] = useState<UploadFile[]>([]);
  const [showSuccess, setShowSuccess] = useState(false);
  const [pdfFiles, setPdfFiles] = useState<UploadFile[]>([]);

  const notesInputRef = useRef<HTMLInputElement | null>(null);
  const quizInputRef = useRef<HTMLInputElement | null>(null);
  const editorRef = useRef<HTMLDivElement | null>(null);
  const pdfInputRef = useRef<HTMLInputElement | null>(null);

  const router = useRouter();

  const handleBack = () => router.back();
  const handleHome = () => router.push("/dashboard");

  // Ensure text direction is fixed on component mount
  useEffect(() => {
    if (editorRef.current) {
      fixTextDirection();
    }
  }, [projectDescription]);

  const handleMultipleFileSelect = (
    e: React.ChangeEvent<HTMLInputElement>,
    setter: React.Dispatch<React.SetStateAction<UploadFile[]>>
  ) => {
    const files = Array.from(e.target.files || []);
    if (files.length === 0) return;
    const validFiles: UploadFile[] = files.map((file) => ({
      name: file.name,
      size: (file.size / (1024 * 1024)).toFixed(2) + " MB",
    }));
    setter((prev) => [...prev, ...validFiles]);
  };

  const handleAddVideoLink = () => {
    if (videoLinkInput.trim()) {
      setVideoLinks([...videoLinks, videoLinkInput.trim()]);
      setVideoLinkInput("");
    }
  };

  const handleSaveSession = () => {
    setShowSuccess(true);
    setTimeout(() => setShowSuccess(false), 3000);
  };

  // Rich text editor functions
  const execCommand = (command: string, value?: string) => {
    document.execCommand(command, false, value);
    editorRef.current?.focus();
    // Update state with new content and fix direction
    setTimeout(() => {
      if (editorRef.current) {
        setProjectDescription(editorRef.current.innerHTML);
        fixTextDirection();
      }
    }, 10);
  };

  const fixTextDirection = () => {
    if (editorRef.current) {
      const editor = editorRef.current;
      editor.style.direction = "ltr";
      editor.style.textAlign = "left";
      editor.style.unicodeBidi = "bidi-override";
      editor.style.writingMode = "horizontal-tb";

      // Fix direction for all child elements
      const allElements = editor.querySelectorAll("*");
      allElements.forEach((el: any) => {
        el.style.direction = "ltr";
        el.style.unicodeBidi = "bidi-override";
        el.style.textAlign = "left";
      });
    }
  };

  const handleFormatBlock = (tag: string) => {
    if (tag) {
      execCommand("formatBlock", `<${tag}>`);
    } else {
      execCommand("formatBlock", "<div>");
    }
  };

  const handleInsertLink = () => {
    const url = prompt("Enter URL:");
    if (url) {
      execCommand("createLink", url);
    }
  };

  const handleInsertImage = () => {
    const url = prompt("Enter image URL:");
    if (url) {
      execCommand("insertImage", url);
    }
  };

  return (
    <>
      <div className="min-h-screen bg-white p-8 font-poppins text-[#000] relative">
        {/* Header */}
        <div className="flex justify-between items-center mb-6">
          <button
            onClick={handleBack}
            className="flex items-center gap-2 border px-4 py-2 text-[15px] font-medium hover:bg-gray-100"
          >
            <Image
              src="/assets/images/back-arrow.png"
              alt="Back"
              width={15}
              height={15}
            />
            Back
          </button>
          <button
            onClick={handleHome}
            className="flex items-center justify-center border w-[31px] h-[31px]"
          >
            <Image
              src="/assets/images/home.png"
              alt="Home"
              width={20}
              height={20}
            />
          </button>
        </div>

        <div className="max-w-[800px] w-full mx-auto px-[20px]">
          <p className="text-xl font-semibold mb-8">
            Module_01 : Introduction to Python Programming
          </p>

          {/* Lesson Title */}
          <div className="mb-8 flex items-center gap-4">
            <label className="text-base font-medium whitespace-nowrap">
              Lesson Title :
            </label>
            <input
              type="text"
              value={lessonTitle}
              onChange={(e) => setLessonTitle(e.target.value)}
              placeholder="Getting Started with Python - Variables, Data Types, and Basic Syntax"
              className="flex-1 border border-gray-400 rounded-md px-4 py-3 text-sm outline-none focus:border-gray-600"
            />
          </div>

          {/* Upload Video */}
          <div className="mb-8">
            <label className="block text-base font-medium mb-3">
              Upload Video :
            </label>

            {/* Video URL Input */}
            <div className="mb-3">
              <div className="flex items-center border border-gray-400 rounded-md px-4 py-3">
                <FaPaperclip className="text-gray-400 mr-3" />
                <input
                  type="text"
                  value={videoLinkInput}
                  onChange={(e) => setVideoLinkInput(e.target.value)}
                  placeholder="Add Vimeo Video URL here"
                  className="flex-1 outline-none text-sm text-gray-600"
                />
              </div>
            </div>

            {/* Video Files List */}
            {videoLinks.length > 0 && (
              <div className="mb-3 space-y-2">
                {videoLinks.map((link, i) => (
                  <div
                    key={i}
                    className="flex items-center justify-between border border-gray-400 rounded-md px-4 py-3"
                  >
                    <div className="flex items-center gap-3">
                      <span className="text-gray-600 font-medium">{i + 1}.</span>
                      <span className="text-blue-600 underline text-sm">
                        Overview_video.mp4 ( 356 GB )
                      </span>
                    </div>
                    <button
                      onClick={() =>
                        setVideoLinks(videoLinks.filter((_, idx) => idx !== i))
                      }
                      className="text-red-500 hover:text-red-700"
                    >
                      <FaTimes />
                    </button>
                  </div>
                ))}
              </div>
            )}

            {/* Upload Button */}
            <div className="flex justify-end">
              <button
                onClick={handleAddVideoLink}
                className="px-4 py-2 border border-blue-400 text-blue-600 rounded text-sm font-medium hover:bg-blue-50"
              >
                Upload
              </button>
            </div>
          </div>

          {/* Upload Notes */}
          <div className="mb-8">
            <label className="block text-base font-medium mb-3">
              Upload Notes :
            </label>

            {/* Upload Area */}
            <div className="mb-3">
              <div
                onClick={() => notesInputRef.current?.click()}
                className="border border-gray-400 rounded-md p-8 cursor-pointer hover:bg-gray-50 transition-colors"
              >
                <div className="flex flex-col items-center justify-center text-center">
                  <div className="w-12 h-12 bg-gray-100 rounded flex items-center justify-center mb-3">
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" className="text-gray-400">
                      <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                      <polyline points="14,2 14,8 20,8" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                    </svg>
                  </div>
                  <input
                    ref={notesInputRef}
                    type="file"
                    accept=".md"
                    multiple
                    className="hidden"
                    onChange={(e) => handleMultipleFileSelect(e, setNotesFile)}
                  />
                  <p className="text-sm text-blue-600 underline mb-1">
                    Choose file or Drag here
                  </p>
                  <p className="text-xs text-gray-500">
                    Supported file type(s) : .md
                  </p>
                  <p className="text-xs text-gray-500">
                    Size limit 20MB per file, up to 10 file(s) with total file size not exceeding 100MB
                  </p>
                </div>
              </div>
            </div>

            {/* Files List */}
            {notesFile.length > 0 && (
              <div className="mb-3">
                {notesFile.map((file, i) => (
                  <div
                    key={i}
                    className="flex items-center justify-between border border-gray-400 rounded-md px-4 py-3 w-full mb-2"
                  >
                    <div className="flex items-center gap-3">
                      <span className="text-gray-600 font-medium">{i + 1}.</span>
                      <span className="text-blue-600 underline text-sm">
                        Lesson_1_notes.md (10 MB )
                      </span>
                    </div>
                    <button
                      onClick={() =>
                        setNotesFile(notesFile.filter((_, idx) => idx !== i))
                      }
                      className="text-red-500 hover:text-red-700 text-lg"
                    >
                      ✕
                    </button>
                  </div>
                ))}
              </div>
            )}

            {/* Upload Button */}
            <div className="flex justify-end mt-4">
              <button
                className="px-6 py-2 border border-blue-400 text-blue-600 rounded text-sm font-medium hover:bg-blue-50"
              >
                Upload
              </button>
            </div>
          </div>

          {/* Upload Quiz */}
          <div className="mb-8">
            <label className="block text-base font-medium mb-3">
              Upload Quiz :
            </label>

            {/* Upload Area */}
            <div className="mb-3">
              <div
                onClick={() => quizInputRef.current?.click()}
                className="border border-gray-400 rounded-md p-8 cursor-pointer hover:bg-gray-50 transition-colors"
              >
                <div className="flex flex-col items-center justify-center text-center">
                  <div className="w-12 h-12 bg-gray-100 rounded flex items-center justify-center mb-3">
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" className="text-gray-400">
                      <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                      <polyline points="14,2 14,8 20,8" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                      <text x="12" y="16" textAnchor="middle" fontSize="8" fill="currentColor">JSON</text>
                    </svg>
                  </div>
                  <input
                    ref={quizInputRef}
                    type="file"
                    accept=".json"
                    multiple
                    className="hidden"
                    onChange={(e) => handleMultipleFileSelect(e, setQuizFiles)}
                  />
                  <p className="text-sm text-blue-600 underline mb-1">
                    Choose file or Drag here
                  </p>
                  <p className="text-xs text-gray-500">
                    Supported file type(s) : .JSON
                  </p>
                  <p className="text-xs text-gray-500">
                    Size limit 20MB per file, up to 20 file(s) with total file size not exceeding 100MB
                  </p>
                </div>
              </div>
            </div>

            {/* Files List */}
            {quizFiles.length > 0 && (
              <div className="mb-3 space-y-2">
                {quizFiles.map((file, i) => (
                  <div
                    key={i}
                    className="flex items-center justify-between border border-gray-400 rounded-md px-4 py-3 w-full"
                  >
                    <div className="flex items-center gap-3">
                      <span className="text-gray-600 font-medium">{i + 1}.</span>
                      <span className="text-blue-600 underline text-sm">
                        {i === 0 ? "Quiz_01.json" : "Quiz+Q2.json"} (10 MB )
                      </span>
                    </div>
                    <button
                      onClick={() =>
                        setQuizFiles(quizFiles.filter((_, idx) => idx !== i))
                      }
                      className="text-red-500 hover:text-red-700 text-lg"
                    >
                      ✕
                    </button>
                  </div>
                ))}
              </div>
            )}

            {/* Upload Button */}
            <div className="flex justify-end mt-4">
              <button
                className="px-6 py-2 border border-blue-400 text-blue-600 rounded text-sm font-medium hover:bg-blue-50"
              >
                Upload
              </button>
            </div>
          </div>

          {/* Upload Project */}
          <div className="mb-8">
            <div className="flex items-center gap-3 mb-3">
              <label className="text-base font-medium">
                Upload Project :
              </label>
              <button
                className="flex items-center gap-1 px-3 py-1 border border-green-500 text-green-600 rounded text-sm font-medium hover:bg-green-50"
              >
                <span className="text-lg">+</span>
                Add
              </button>
            </div>

            {/* Expandable Project Section */}

                <div className="bg-gray-100   p-6 mb-4">
                   {/* Project Description */}
           <div className="mb-8">
            <label className="block text-base font-medium mb-3">
              Project description :
            </label>
            <div className="bg-gray-100 border border-gray-300 rounded-md">
              {/* Functional Rich Text Editor Toolbar */}
              <div className="border-b border-gray-300 p-3 bg-gray-50 flex items-center gap-1 flex-wrap">
                <button
                  type="button"
                  onClick={() => execCommand("undo")}
                  className="p-1 hover:bg-gray-200 rounded text-sm"
                  title="Undo"
                >
                  ↶
                </button>
                <button
                  type="button"
                  onClick={() => execCommand("redo")}
                  className="p-1 hover:bg-gray-200 rounded text-sm"
                  title="Redo"
                >
                  ↷
                </button>
                <div className="w-px h-4 bg-gray-300 mx-1"></div>

                <select
                  onChange={(e) => handleFormatBlock(e.target.value)}
                  className="text-xs border rounded px-1 py-0.5"
                  defaultValue=""
                >
                  <option value="">Normal text</option>
                  <option value="h1">Heading 1</option>
                  <option value="h2">Heading 2</option>
                  <option value="h3">Heading 3</option>
                </select>

                <div className="w-px h-4 bg-gray-300 mx-1"></div>

                <button
                  type="button"
                  onClick={() => execCommand("bold")}
                  className="p-1 hover:bg-gray-200 rounded"
                  title="Bold"
                >
                  <FaBold size={12} />
                </button>
                <button
                  type="button"
                  onClick={() => execCommand("italic")}
                  className="p-1 hover:bg-gray-200 rounded"
                  title="Italic"
                >
                  <FaItalic size={12} />
                </button>
                <button
                  type="button"
                  onClick={() => execCommand("underline")}
                  className="p-1 hover:bg-gray-200 rounded"
                  title="Underline"
                >
                  <FaUnderline size={12} />
                </button>
                <button
                  type="button"
                  onClick={() => execCommand("strikeThrough")}
                  className="p-1 hover:bg-gray-200 rounded"
                  title="Strikethrough"
                >
                  <FaStrikethrough size={12} />
                </button>

                <div className="w-px h-4 bg-gray-300 mx-1"></div>

                <button
                  type="button"
                  onClick={() => execCommand("insertUnorderedList")}
                  className="p-1 hover:bg-gray-200 rounded"
                  title="Bullet List"
                >
                  <FaListUl size={12} />
                </button>
                <button
                  type="button"
                  onClick={() => execCommand("insertOrderedList")}
                  className="p-1 hover:bg-gray-200 rounded"
                  title="Numbered List"
                >
                  <FaListOl size={12} />
                </button>

                <div className="w-px h-4 bg-gray-300 mx-1"></div>

                <button
                  type="button"
                  onClick={handleInsertLink}
                  className="p-1 hover:bg-gray-200 rounded"
                  title="Insert Link"
                >
                  <FaLink size={12} />
                </button>
                <button
                  type="button"
                  onClick={handleInsertImage}
                  className="p-1 hover:bg-gray-200 rounded"
                  title="Insert Image"
                >
                  <FaImage size={12} />
                </button>
                <button
                  type="button"
                  onClick={() => execCommand("formatBlock", "<pre>")}
                  className="p-1 hover:bg-gray-200 rounded"
                  title="Code Block"
                >
                  <FaCode size={12} />
                </button>
                <button
                  type="button"
                  onClick={() => execCommand("formatBlock", "<blockquote>")}
                  className="p-1 hover:bg-gray-200 rounded"
                  title="Quote"
                >
                  <FaQuoteLeft size={12} />
                </button>
              </div>

              {/* Rich Text Editor Content Area */}
              <div
                ref={editorRef}
                contentEditable
                suppressContentEditableWarning={true}
                onInput={(e) => {
                  const content = e.currentTarget.innerHTML;
                  setProjectDescription(content);
                  // Fix direction immediately after input
                  setTimeout(() => fixTextDirection(), 5);
                }}
                onKeyUp={() => {
                  // Fix direction after key release
                  setTimeout(() => fixTextDirection(), 5);
                }}
                className="w-full min-h-[50px] p-4 outline-none rich-text-editor"
                style={{
                  fontSize: "14px",
                  lineHeight: "1.5",
                  fontFamily: "inherit",
                  direction: "ltr",
                  textAlign: "left",
                  writingMode: "horizontal-tb",
                  unicodeBidi: "bidi-override",
                }}
                dir="ltr"
                lang="en"
                spellCheck="false"
                data-placeholder="Type here...."
                onFocus={(e) => {
                  if (!projectDescription) {
                    e.currentTarget.innerHTML = "";
                  }
                }}
                onBlur={(e) => {
                  if (e.currentTarget.textContent?.trim() === "") {
                    setProjectDescription("");
                    e.currentTarget.innerHTML = "";
                  }
                }}
              />
            </div>
          </div>

                  {/* Upload PDF */}
                  <div className="mb-0">
                    <label className="block text-base font-medium mb-3">
                      Upload PDF <span className="text-gray-400 text-sm">(optional)</span> :
                    </label>
                    <div
                      onClick={() => pdfInputRef.current?.click()}
                      className="bg-gray-100 border border-gray-400 rounded-md p-8 cursor-pointer hover:bg-gray-200 transition-colors"
                    >
                      <div className="flex flex-col items-center justify-center text-center">
                        <div className="w-12 h-12 bg-gray-100 rounded flex items-center justify-center mb-3">
                          <svg width="24" height="24" viewBox="0 0 24 24" fill="none" className="text-gray-400">
                            <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                            <polyline points="14,2 14,8 20,8" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                            <text x="12" y="16" textAnchor="middle" fontSize="6" fill="currentColor">PDF</text>
                          </svg>
                        </div>
                        <input
                          ref={pdfInputRef}
                          type="file"
                          accept=".pdf"
                          multiple
                          className="hidden"
                          onChange={(e) => handleMultipleFileSelect(e, setPdfFiles)}
                        />
                        <p className="text-sm text-blue-600 underline mb-1">
                          Choose file or Drag here
                        </p>
                        <p className="text-xs text-gray-500">
                          Supported file type(s) : .PDF, DOCX, MD
                        </p>
                        <p className="text-xs text-gray-500">
                          Size limit 20MB per file, up to 10 file(s) with total file size not exceeding 100MB
                        </p>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Upload Button - Outside container, bottom right */}
                <div className="flex justify-end mt-4">
                  <button className="px-6 py-2 border border-blue-400 text-blue-600 rounded text-sm font-medium hover:bg-blue-50">
                    Upload
                  </button>
                </div>

          </div>



          {/* Save Button */}
          <div className="flex justify-end mt-12">
            <button
              onClick={handleSaveSession}
              disabled={
                !lessonTitle ||
                !projectDescription ||
                !videoLinks.length ||
                !notesFile.length ||
                !quizFiles.length
              }
              className={`text-white font-medium text-lg px-12 py-3 rounded-md transition-colors ${
                lessonTitle &&
                projectDescription &&
                videoLinks.length &&
                notesFile.length &&
                quizFiles.length
                  ? "bg-[#0A5224] hover:bg-green-700"
                  : "bg-[#0A522480] cursor-not-allowed"
              }`}
            >
              Save Lesson
            </button>
          </div>
        </div>

        {/* Success Popup */}
        {showSuccess && (
          <div className="fixed top-[24px] right-[24px] w-[361px] bg-[#D1FADF] text-[#027A48] p-4 rounded-md shadow-lg flex items-start gap-3 z-50 border border-[#A6F4C5]">
            <BsCheckCircleFill size={24} className="mt-1" />
            <div>
              <p className="font-semibold text-base">Successfully Saved</p>
            </div>
            {/* Animated bottom border */}
            <div
              className="absolute bottom-0 left-0 h-[4px] bg-[#0A5224] animate-borderGrow"
              style={{ width: "100%" }}
            ></div>
          </div>
        )}
      </div>
      <style jsx>{`
        @keyframes borderGrow {
          0% {
            transform: scaleX(0);
          }
          100% {
            transform: scaleX(1);
          }
        }

        .animate-borderGrow {
          transform-origin: left;
          animation: borderGrow 1s ease-out forwards;
        }

        [contenteditable]:empty:before {
          content: attr(data-placeholder);
          color: #999;
          pointer-events: none;
        }

        .rich-text-editor {
          direction: ltr !important;
          text-align: left !important;
          unicode-bidi: bidi-override !important;
          writing-mode: horizontal-tb !important;
        }

        .rich-text-editor * {
          direction: ltr !important;
          unicode-bidi: bidi-override !important;
          text-align: left !important;
        }

        .rich-text-editor p,
        .rich-text-editor div,
        .rich-text-editor span,
        .rich-text-editor strong,
        .rich-text-editor em,
        .rich-text-editor u {
          direction: ltr !important;
          unicode-bidi: bidi-override !important;
          text-align: left !important;
        }
      `}</style>
    </>
  );
};

export default AddLessionPage;
