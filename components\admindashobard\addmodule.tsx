'use client';

import React, { useState, useRef, useEffect } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { FaUpload, FaTimes } from "react-icons/fa";
import Image from "next/image";
import { BsCheckCircleFill } from "react-icons/bs";
import { useCreateModule } from "@/api-services/modules/modules";
import { useGetCourses } from "@/api-services/courses/courses";
import { useQueryClient } from "@tanstack/react-query";
import toast from "react-hot-toast";

const AddModulePage = () => {
  const [moduleTitle, setModuleTitle] = useState("");
  const [uploadedFiles, setUploadedFiles] = useState<
    { name: string; size: string; file: File }[]
  >([]);
  const [isUploaded, setIsUploaded] = useState(false);
  const [showSuccess, setShowSuccess] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [course, setCourse] = useState<any>(null);

  const fileInputRef = useRef<HTMLInputElement | null>(null);
  const router = useRouter();
  const searchParams = useSearchParams();
  const courseId = searchParams.get('courseId');

  // React Query hooks
  const queryClient = useQueryClient();
  const createModuleMutation = useCreateModule();
  const { data: coursesResponse } = useGetCourses();

  // Find course by ID
  useEffect(() => {
    if (coursesResponse?.data && courseId) {
      const foundCourse = coursesResponse.data.find(c => c.id === courseId);
      if (foundCourse) {
        setCourse(foundCourse);
      } else {
        toast.error('Course not found');
        router.push('/Admin/dashboard');
      }
    }
  }, [coursesResponse, courseId, router]);

  const handleBack = () => router.back();
  const handleHome = () => router.push("/dashboard");

  const isFileValid = (file: File) => {
    const isMd = file.name.endsWith(".md");
    const isSizeValid = file.size <= 20 * 1024 * 1024;
    return isMd && isSizeValid;
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (files) {
      const validFiles = Array.from(files).filter(isFileValid);
      const newFiles = validFiles.map((file) => ({
        name: file.name,
        size: (file.size / (1024 * 1024)).toFixed(2) + " MB",
        file: file,
      }));
      setUploadedFiles((prev) => [...prev, ...newFiles]);
      if (validFiles.length > 0) setIsUploaded(true);
    }
  };

  const handleDrop = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    const files = e.dataTransfer.files;
    if (files) {
      const validFiles = Array.from(files).filter(isFileValid);
      const newFiles = validFiles.map((file) => ({
        name: file.name,
        size: (file.size / (1024 * 1024)).toFixed(2) + " MB",
        file: file,
      }));
      setUploadedFiles((prev) => [...prev, ...newFiles]);
      if (validFiles.length > 0) setIsUploaded(true);
    }
  };

  const handleDragOver = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
  };

  const triggerFileInput = () => {
    if (fileInputRef.current) fileInputRef.current.click();
  };

  const removeFile = (index: number) => {
    const updatedFiles = uploadedFiles.filter((_, i) => i !== index);
    setUploadedFiles(updatedFiles);
    if (updatedFiles.length === 0) setIsUploaded(false);
  };

  const handleSaveModule = async () => {
    if (!moduleTitle.trim()) {
      toast.error('Module title is required');
      return;
    }

    if (!courseId) {
      toast.error('Course ID is missing');
      return;
    }

    if (uploadedFiles.length === 0) {
      toast.error('Please upload at least one overview file');
      return;
    }

    setIsSubmitting(true);

    try {
      // For now, we'll use the first uploaded file as overview
      // In a real implementation, you might want to combine multiple files or handle them differently
      const overviewFile = uploadedFiles[0].file;
      const overviewText = await overviewFile.text();

      // Try FormData approach for file upload
      const formData = new FormData();
      formData.append('title', moduleTitle.trim());
      formData.append('overview_md', overviewFile, overviewFile.name);

      // Also try JSON payload as backup
      const jsonPayload = {
        title: moduleTitle.trim(),
        overview_md: overviewText.trim(),
      };

      // Log detailed information for debugging
      console.log('CourseId:', courseId);
      console.log('Module title:', moduleTitle.trim());
      console.log('Overview text length:', overviewText.length);
      console.log('Overview text preview:', overviewText.substring(0, 100) + '...');

      console.log('Creating module with FormData');
      console.log('Creating module with JSON payload:', jsonPayload);

      // Try FormData first
      let response;
      try {
        console.log('Trying FormData approach...');
        response = await createModuleMutation.mutateAsync({
          courseId: courseId,
          payload: formData
        });
        console.log('FormData approach successful:', response);
      } catch (formDataError) {
        console.log('FormData approach failed, trying JSON:', formDataError);

        // Fallback to JSON payload
        response = await createModuleMutation.mutateAsync({
          courseId: courseId,
          payload: jsonPayload
        });
        console.log('JSON approach successful:', response);
      }

      console.log('Module creation response:', response);
      toast.success('Module created successfully!');

      // Invalidate modules cache to refresh the list
      queryClient.invalidateQueries({ queryKey: ["modules", courseId] });

      // Show success animation
      setShowSuccess(true);
      setTimeout(() => {
        setShowSuccess(false);
        // Navigate back to course view or modules list
        router.push(`/Admin/dashboard/viewcourse?id=${courseId}`);
      }, 2000);

    } catch (error) {
      console.error('Error creating module:', error);

      if (error instanceof Error) {
        toast.error(error.message || 'Failed to create module. Please try again.');
      } else {
        toast.error('Failed to create module. Please try again.');
      }
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <>
      <div className="container p-8 font-poppins text-[#000] relative">
        {/* Header */}
        <div className="flex justify-between items-center mb-6">
          <button
            onClick={handleBack}
            className="flex items-center gap-2 border px-4 py-2 text-[15px] font-medium hover:bg-gray-100"
          >
            <Image src="/assets/images/back-arrow.png" alt="Back" width={15} height={15} />
            Back
          </button>
          <button
            onClick={handleHome}
            className="flex items-center justify-center border w-[31px] h-[31px]"
          >
            <Image src="/assets/images/home.png" alt="Home" width={20} height={20} />
          </button>
        </div>

        <div className="md:max-w-[900px] w-full px-[20px]">
          <p className="text-xl font-semibold mb-6">
            {course ? `Course: ${course.name}` : 'Loading course...'}
          </p>

          {/* Module title inline */}
          <div className="flex items-center mb-6 gap-4">
            <label htmlFor="moduleTitle" className="text-lg whitespace-nowrap font-normal">
              Module title :
            </label>
            <input
              id="moduleTitle"
              type="text"
              value={moduleTitle}
              onChange={(e) => setModuleTitle(e.target.value)}
              className="border border-black rounded-sm px-4 py-2 text-sm w-[600px]"
            />
          </div>

          {/* Upload Overview */}
          <div className="mb-4">
            <p className="text-lg mb-2">Upload Overview Text :</p>
            <div
              onClick={triggerFileInput}
              onDrop={handleDrop}
              onDragOver={handleDragOver}
              className="border border-black rounded-md p-10 flex flex-col items-center justify-center text-center cursor-pointer"
            >
              <FaUpload className="text-gray-500 mb-2" size={30} />
              <input
                ref={fileInputRef}
                type="file"
                onChange={handleFileChange}
                className="hidden"
                accept=".md"
                multiple
              />
              <p className="text-sm text-blue-600 underline cursor-pointer">
                Choose file
              </p>
              <span className="text-sm">or <strong>Drag here</strong></span>
              <p className="text-xs text-gray-500 mt-2">
                Supported file type(s) : .md <br />
                Size limit 20MB per file, up to 10 file(s) with total file size not exceeding 100MB
              </p>
            </div>

            {/* Upload Button */}
            <div className="flex justify-end mt-4">
              <button
                className={`px-5 py-2 rounded-sm font-medium text-sm transition-colors duration-200 ${
                  isUploaded
                    ? "text-[#00A13B] border border-[#00A13B] cursor-default"
                    : "text-[#063585] border border-[#063585] hover:bg-blue-50"
                }`}
                disabled={isUploaded}
              >
                {isUploaded ? "Uploaded" : "Upload"}
              </button>
            </div>

            {/* Uploaded files list */}
            {uploadedFiles.length > 0 && (
              <div className="mt-4 space-y-2">
                {uploadedFiles.map((file, index) => (
                  <div
                    key={index}
                    className="flex items-center justify-between border rounded-md px-3 py-2"
                  >
                    <div className="flex items-center gap-2">
                      <span className="font-semibold">{index + 1}.</span>
                      <span className="text-blue-600 underline cursor-pointer">{file.name}</span>
                      <span className="text-xs text-gray-500">({file.size})</span>
                    </div>
                    <button
                      onClick={() => removeFile(index)}
                      className="text-red-500 hover:text-red-700"
                    >
                      <FaTimes size={18} />
                    </button>
                  </div>
                ))}
              </div>
            )}
          </div>

          {/* Add Lesson & Save Button */}
          <div className="flex flex-col lg:flex-row sm:justify-between sm:items-center gap-4 mt-10">
            <div className="flex items-center gap-4">
              <span className="text-[16px] font-medium">Add Lesson :</span>
              <button onClick={() => router.push(`/Admin/dashboard/addlession?courseId=${courseId}`)}
              className="border border-[#0A5224] text-[#0A5224] font-medium rounded-sm px-6 py-2 text-base flex items-center gap-2 hover:bg-[#0A5224] hover:text-[#fff]">
                <span className="text-xl font-bold">+</span> Add Lesson
              </button>
            </div>
            <button
              onClick={handleSaveModule}
              className={`text-white font-medium text-[20px] px-8 py-3 rounded-[5px] transition-colors ${
                moduleTitle && isUploaded && !isSubmitting
                  ? "bg-[#0A5224] hover:bg-green-700"
                  : "bg-[#0A522480] cursor-not-allowed"
              }`}
              disabled={!moduleTitle || !isUploaded || isSubmitting}
            >
              {isSubmitting ? 'Saving...' : 'Save Module'}
            </button>
          </div>
        </div>


      </div>

 {showSuccess && (
  <div className="fixed top-[24px] right-[24px] w-[361px] bg-[#D1FADF] text-[#027A48] p-4 rounded-md shadow-lg flex items-start gap-3 z-50 border border-[#A6F4C5]">
    <BsCheckCircleFill size={24} className="mt-1" />
    <div>
      <p className="font-semibold text-base">Successfully Saved</p>
    </div>
    {/* Animated bottom border */}
    <div className="absolute bottom-0 left-0 h-[4px] bg-[#0A5224] animate-borderGrow" style={{ width: '100%' }}></div>
  </div>
)}
<style jsx>{`
  @keyframes borderGrow {
    0% {
      transform: scaleX(0);
    }
    100% {
      transform: scaleX(1);
    }
  }

  .animate-borderGrow {
    transform-origin: left;
    animation: borderGrow 1s ease-out forwards;
  }
`}</style>

    </>
  );
};

export default AddModulePage;
