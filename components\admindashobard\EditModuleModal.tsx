"use client";

import React, { useState, useEffect, useRef } from 'react';
import { useUpdateModule, useGetModules } from '@/api-services/modules/modules';
import { useQueryClient } from '@tanstack/react-query';
import { FaUpload, FaTimes } from "react-icons/fa";
import toast from 'react-hot-toast';

interface Module {
  id: string;
  title: string;
  overview_md: string;
  course: string;
  created_by: number;
  created_at: string;
}

interface EditModuleModalProps {
  isOpen: boolean;
  onClose: () => void;
  module: Module | null;
  courseId: string;
  onSuccess?: () => void;
}

const EditModuleModal: React.FC<EditModuleModalProps> = ({ 
  isOpen, 
  onClose, 
  module, 
  courseId, 
  onSuccess 
}) => {
  const [moduleTitle, setModuleTitle] = useState('');
  const [uploadedFiles, setUploadedFiles] = useState<
    { name: string; size: string; file: File }[]
  >([]);
  const [isUploaded, setIsUploaded] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [titleError, setTitleError] = useState('');

  const fileInputRef = useRef<HTMLInputElement | null>(null);

  // React Query hooks
  const queryClient = useQueryClient();
  const updateModuleMutation = useUpdateModule();
  const { data: modulesResponse } = useGetModules(courseId);

  // Initialize form with module data
  useEffect(() => {
    if (module && isOpen) {
      setModuleTitle(module.title);
      setUploadedFiles([]);
      setIsUploaded(false);
      setTitleError('');
    }
  }, [module, isOpen]);

  if (!isOpen || !module) return null;

  // Check for duplicate module title
  const checkDuplicateTitle = (title: string): boolean => {
    if (!modulesResponse?.data) return false;
    return modulesResponse.data.some(
      (m: Module) => m.title.toLowerCase() === title.toLowerCase() && m.id !== module.id
    );
  };

  // Handle module title change with real-time validation
  const handleModuleTitleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setModuleTitle(value);

    // Clear previous error
    setTitleError('');

    // Check for duplicate title if value is not empty and different from original
    if (value.trim() && value.trim() !== module.title && checkDuplicateTitle(value.trim())) {
      setTitleError(`Module title "${value.trim()}" already exists`);
    }
  };

  // File validation
  const isFileValid = (file: File) => {
    const isMd = file.name.endsWith(".md");
    const isSizeValid = file.size <= 20 * 1024 * 1024;
    return isMd && isSizeValid;
  };

  // Handle file upload
  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (files) {
      const validFiles = Array.from(files).filter(isFileValid);
      const newFiles = validFiles.map((file) => ({
        name: file.name,
        size: (file.size / (1024 * 1024)).toFixed(2) + " MB",
        file: file,
      }));
      setUploadedFiles(newFiles);
      if (validFiles.length > 0) setIsUploaded(true);
    }
  };

  // Handle drag and drop
  const handleDrop = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    const files = e.dataTransfer.files;
    if (files) {
      const validFiles = Array.from(files).filter(isFileValid);
      const newFiles = validFiles.map((file) => ({
        name: file.name,
        size: (file.size / (1024 * 1024)).toFixed(2) + " MB",
        file: file,
      }));
      setUploadedFiles(newFiles);
      if (validFiles.length > 0) setIsUploaded(true);
    }
  };

  const handleDragOver = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
  };

  const triggerFileInput = () => {
    if (fileInputRef.current) fileInputRef.current.click();
  };

  const removeFile = (index: number) => {
    const updatedFiles = uploadedFiles.filter((_, i) => i !== index);
    setUploadedFiles(updatedFiles);
    if (updatedFiles.length === 0) setIsUploaded(false);
  };

  // Reset form fields
  const resetForm = () => {
    if (module) {
      setModuleTitle(module.title);
      setUploadedFiles([]);
      setIsUploaded(false);
      setIsSubmitting(false);
      setTitleError('');
    }
  };

  // Handle modal close
  const handleClose = () => {
    if (!isSubmitting) {
      resetForm();
      onClose();
    }
  };

  // Handle form submission
  const handleSubmit = async () => {
    // Validation
    if (!moduleTitle.trim()) {
      toast.error('Module title is required');
      return;
    }

    // Check for duplicate module title
    if (moduleTitle.trim() !== module.title && checkDuplicateTitle(moduleTitle.trim())) {
      toast.error(`Module title "${moduleTitle.trim()}" already exists. Please choose a different title.`);
      return;
    }

    setIsSubmitting(true);

    try {
      let payload: any = {
        title: moduleTitle.trim(),
      };

      // If new overview file is uploaded, include it
      if (uploadedFiles.length > 0) {
        const overviewFile = uploadedFiles[0].file;
        const overviewText = await overviewFile.text();
        payload.overview_md = overviewText.trim();
      }

      // Check authentication before making request
      const token = localStorage.getItem("token");
      if (!token) {
        toast.error('You are not logged in as admin. Please login first.');
        return;
      }

      console.log('Updating module with payload:', payload);
      const response = await updateModuleMutation.mutateAsync({
        courseId: courseId,
        moduleId: module.id,
        payload: payload
      });
      console.log('Module update response:', response);

      toast.success('Module updated successfully!');

      // Invalidate modules cache to refresh the list
      console.log('Invalidating modules cache after module update...');
      queryClient.invalidateQueries({ queryKey: ["modules", courseId] });
      console.log('Modules cache invalidated - list will refresh automatically');

      resetForm();
      onClose();

      // Call success callback if provided
      if (onSuccess) {
        onSuccess();
      }

    } catch (error) {
      console.error('Error updating module:', error);

      if (error instanceof Error) {
        toast.error(error.message || 'Failed to update module. Please try again.');
      } else {
        toast.error('Failed to update module. Please try again.');
      }
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div
      className="fixed inset-0 flex items-center justify-center bg-[#0000007d] z-[99] lg:pl-90"
      onClick={handleClose}
    >
      <div
        className="bg-[#F3F3F3] p-6 w-[746px] max-w-[90vw] max-h-[90vh] overflow-y-auto mx-4"
        onClick={(e) => e.stopPropagation()}
      >
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-[17px] underline font-semibold">Edit Module</h2>
          <button
            onClick={handleClose}
            disabled={isSubmitting}
            className="text-red text-[30px] disabled:opacity-50 disabled:cursor-not-allowed"
          >
            &times;
          </button>
        </div>

        <div className="space-y-4">
          {/* Module Title */}
          <div>
            <label htmlFor="moduleTitle" className="text-sm font-medium mb-2 block">
              Module Title <span className="text-red-500">*</span>
            </label>
            <input
              id="moduleTitle"
              type="text"
              value={moduleTitle}
              onChange={handleModuleTitleChange}
              disabled={isSubmitting}
              className={`border rounded-md px-4 py-2 w-full ${
                titleError ? 'border-red-500' : 'border-gray-300'
              } disabled:opacity-50 disabled:cursor-not-allowed`}
              placeholder="Enter module title"
            />
            {titleError && (
              <p className="text-red-500 text-xs mt-1">{titleError}</p>
            )}
          </div>

          {/* Current Overview Display */}
          <div>
            <label className="text-sm font-medium mb-2 block">
              Current Overview
            </label>
            <div className="border rounded-md p-4 bg-gray-50 max-h-32 overflow-y-auto">
              <p className="text-sm text-gray-700 whitespace-pre-wrap">
                {module.overview_md || 'No overview available'}
              </p>
            </div>
          </div>

          {/* Upload New Overview (Optional) */}
          <div>
            <label className="text-sm font-medium mb-2 block">
              Upload New Overview (Optional)
            </label>
            <div
              onClick={triggerFileInput}
              onDrop={handleDrop}
              onDragOver={handleDragOver}
              className="border border-dashed border-gray-400 rounded-md p-6 flex flex-col items-center justify-center text-center cursor-pointer hover:bg-gray-50"
            >
              <FaUpload className="text-gray-500 mb-2" size={24} />
              <input
                ref={fileInputRef}
                type="file"
                onChange={handleFileChange}
                className="hidden"
                accept=".md"
              />
              <p className="text-sm text-blue-600 underline cursor-pointer">
                Choose file
              </p>
              <span className="text-sm">or <strong>Drag here</strong></span>
              <p className="text-xs text-gray-500 mt-2">
                Supported file type: .md (Max 20MB)
              </p>
            </div>

            {/* Uploaded Files List */}
            {uploadedFiles.length > 0 && (
              <div className="mt-4 space-y-2">
                {uploadedFiles.map((file, index) => (
                  <div
                    key={index}
                    className="flex items-center justify-between border rounded-md p-3 bg-green-50"
                  >
                    <div>
                      <p className="text-sm font-medium">{file.name}</p>
                      <p className="text-xs text-gray-500">{file.size}</p>
                    </div>
                    <button
                      onClick={() => removeFile(index)}
                      className="text-red-500 hover:text-red-700"
                      aria-label={`Remove ${file.name}`}
                    >
                      <FaTimes size={16} />
                    </button>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex justify-end gap-4 mt-6">
          <button
            onClick={handleClose}
            disabled={isSubmitting}
            className="px-6 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            Cancel
          </button>
          <button
            onClick={handleSubmit}
            disabled={isSubmitting || !!titleError}
            className="px-6 py-2 bg-[#0A5224] text-white rounded-md hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {isSubmitting ? 'Updating...' : 'Update Module'}
          </button>
        </div>
      </div>
    </div>
  );
};

export default EditModuleModal;
