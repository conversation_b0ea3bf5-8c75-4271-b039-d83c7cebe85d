"use client";

import React, { useState, useEffect } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { FaTimes } from "react-icons/fa";
import Image from "next/image";
import { useGetCourses } from "@/api-services/courses/courses";
import { useGetModules } from "@/api-services/modules/modules";
import toast from "react-hot-toast";
import EditCourseModal from "./EditCourseModal";

interface Module {
  id: string;
  title: string;
  overview_md: string;
  course: string;
  created_by: number;
  created_at: string;
}

interface Course {
  id: string;
  name: string;
  tags: string[];
  thumbnail: string;
  price: string;
  is_active: boolean;
  is_published: boolean;
  created_by: {
    id: number;
    name: string;
    email: string;
  };
  created_at: string;
}

// Modules will be fetched from API

const ViewCoursePage = () => {
  const [course, setCourse] = useState<Course | null>(null);
  const [loading, setLoading] = useState(true);
  const [selectedModules, setSelectedModules] = useState<Module[]>([]);
  const [showNoModuleDropdown, setShowNoModuleDropdown] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const router = useRouter();
  const searchParams = useSearchParams();
  const courseId = searchParams.get('id');

  // Fetch courses data
  const { data: coursesResponse, isLoading } = useGetCourses();

  // Fetch modules data for the specific course
  const { data: modulesResponse, isLoading: modulesLoading } = useGetModules(courseId || '');

  // Find the specific course by ID
  useEffect(() => {
    if (coursesResponse?.data && courseId) {
      const foundCourse = coursesResponse.data.find(c => c.id === courseId);
      if (foundCourse) {
        setCourse(foundCourse);
      } else {
        toast.error('Course not found');
        router.push('/Admin/dashboard');
      }
      setLoading(false);
    } else if (!isLoading && coursesResponse?.data) {
      setLoading(false);
    }
  }, [coursesResponse, courseId, router, isLoading]);

  const handleBack = () => router.back();
  const handleHome = () => router.push("/dashboard");
  const handleaddModule = () => {
    if (courseId) {
      router.push(`/Admin/dashboard/addmodule?courseId=${courseId}`);
    } else {
      toast.error('Course ID is missing');
    }
  };

  // Get all modules from API
  const allModules = modulesResponse?.data || [];

  // Modules not selected yet
  const availableModules = allModules.filter(
    (m) => !selectedModules.some((sm) => sm.id === m.id)
  );

  // Select module handler
  const onSelectModule = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const moduleId = e.target.value;
    if (!moduleId) return;
    const module = allModules.find((m) => m.id === moduleId);
    if (module) {
      setSelectedModules([...selectedModules, module]);
      setShowNoModuleDropdown(false); // close no module message if open
    }
  };

  // Remove module from selected list
  const onRemoveModule = (id: string) => {
    setSelectedModules(selectedModules.filter((m) => m.id !== id));
  };

  // Add Lesson button handler (dummy alert)
  const onAddLesson = () => {
    alert("Add Lesson button clicked");
  };

  // When user clicks on select input and no modules available
  const onSelectClick = () => {
    if (availableModules.length === 0) {
      setShowNoModuleDropdown((prev) => !prev);
    } else {
      setShowNoModuleDropdown(false);
    }
  };

  return (
    <div className="container p-8">
      {/* Header */}
      <div className="flex justify-between items-center mb-6">
        <button
          onClick={handleBack}
          className="flex items-center gap-2 border px-4 py-2 text-[15px] font-medium hover:bg-gray-100"
        >
          <Image src="/assets/images/back-arrow.png" alt="Back" width={15} height={15} />
          Back
        </button>
        <button
          onClick={handleHome}
          className="flex items-center justify-center border w-[31px] h-[31px]"
        >
          <Image src="/assets/images/home.png" alt="Home" width={20} height={20} />
        </button>
      </div>

      <div className="md:max-w-[658px] w-full px-[20px]">
        <div className="flex justify-between items-center mb-4">
          <h1 className="text-2xl font-semibold">View Course</h1>
          <button
            onClick={() => setIsEditModalOpen(true)}
            disabled={!course}
            className="flex items-center gap-2 border border-[#0A5224] text-[#0A5224] px-4 py-2 text-[15px] font-medium hover:bg-[#0A5224] hover:text-white disabled:opacity-50 disabled:cursor-not-allowed"
          >
            Edit Course
          </button>
        </div>

        {loading || modulesLoading ? (
          <div className="flex items-center justify-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[#0A5224]"></div>
            <span className="ml-2">Loading course and modules...</span>
          </div>
        ) : !course ? (
          <div className="text-center py-8">
            <p className="text-red-600">Course not found</p>
          </div>
        ) : (
          <div className="space-y-6">
            {/* Course Name */}
            <div>
              <label htmlFor="courseName" className="text-sm font-medium mb-2 block">
                Course Name
              </label>
              <input
                id="courseName"
                value={course.name}
                readOnly
                className="border rounded-md px-4 py-2 w-full mb-4 bg-gray-50"
              />
            </div>

            {/* Cover Image Display */}
            <div>
              <label className="text-sm font-medium mb-2 block">Cover Image</label>
              <div className="border rounded-md p-4 bg-gray-50">
                {course.thumbnail ? (
                  <div className="flex items-center justify-center">
                    <Image
                      src={course.thumbnail}
                      alt={course.name}
                      width={200}
                      height={120}
                      className="rounded-md object-cover"
                    />
                  </div>
                ) : (
                  <div className="text-center py-8 text-gray-500">
                    <p>No cover image uploaded</p>
                  </div>
                )}
              </div>
            </div>

            {/* Tag and Price */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-[100px] mb-4">
              <div>
                <label className="text-sm font-medium block mb-1">
                  Tag
                </label>
                <input
                  type="text"
                  value={course.tags.length > 0 ? course.tags[0] : 'No tag'}
                  readOnly
                  className="w-full border rounded-md p-2 text-sm bg-gray-50"
                />
              </div>

              <div>
                <label className="text-sm font-medium block mb-1">Price</label>
                <div className="relative">
                  <input
                    type="text"
                    value={course.price === '0.00' ? 'Free' : `₹${course.price}`}
                    readOnly
                    className="w-full border rounded-md px-6 py-2 text-sm bg-gray-50"
                  />
                </div>
              </div>
            </div>

          {/* Selected Modules List */}
          {selectedModules.length > 0 && (
            <div className="mb-4 space-y-4">
              {selectedModules.map((module, index) => (
                <div
                  key={module.id}
                  className="border rounded-md p-4 bg-gray-100 relative"
                >
                  <span className="font-semibold mr-2">{index + 1}.</span>
                  <span className="font-semibold">{module.title}</span>

                  <button
                    onClick={() => onRemoveModule(module.id)}
                    className="absolute top-3 right-3 text-red-500 hover:text-red-700"
                    aria-label={`Remove ${module.title}`}
                  >
                    <FaTimes size={20} />
                  </button>

                  <p className="mt-2 text-gray-700">{module.overview_md || 'No description available'}</p>
                </div>
              ))}
            </div>
          )}

       {/* Add Lesson Button Beside Module List */}
{selectedModules.length > 0 && (
  <div className="flex justify-end mb-6">
    <button
      onClick={() => router.push(`/Admin/dashboard/addlession?courseId=${courseId}`)}
      className="border border-[#0A5224] text-[#0A5224] px-6 py-2 text-[16px] font-medium hover:bg-[#0A5224] hover:text-white rounded"
    >
      Add Lesson
    </button>
  </div>
)}

          {/* Select Module Dropdown */}
          <div className="relative">
            <label className="text-sm font-medium block mb-1">
              Select Module
              {modulesLoading && <span className="text-xs text-gray-500">(Loading...)</span>}
            </label>
            <select
              value=""
              onChange={onSelectModule}
              onClick={onSelectClick}
              disabled={modulesLoading}
              className={`w-full border rounded-md p-2 text-sm cursor-pointer disabled:opacity-50 disabled:cursor-not-allowed`}
            >
              <option value="">
                {modulesLoading ? 'Loading modules...' : 'Select Module'}
              </option>
              {availableModules.map((module) => (
                <option key={module.id} value={module.id}>
                  {module.title}
                </option>
              ))}
            </select>

            {/* Custom dropdown message when no modules */}
            {showNoModuleDropdown && availableModules.length === 0 && !modulesLoading && (
              <div className="absolute z-10 left-0 right-0 mt-1 rounded border border-gray-300 bg-gray-100 text-gray-600 text-center py-2 select-none pointer-events-none">
                No modules available for this course
              </div>
            )}
          </div>

          {/* Modules Summary */}
          <div className="bg-blue-50 border border-blue-200 rounded-md p-4">
            <h3 className="text-sm font-medium text-blue-800 mb-2">Course Modules Summary</h3>
            <div className="text-sm text-blue-700">
              <p>Total Modules: <span className="font-semibold">{allModules.length}</span></p>
              <p>Selected Modules: <span className="font-semibold">{selectedModules.length}</span></p>
              <p>Available to Select: <span className="font-semibold">{availableModules.length}</span></p>
            </div>
          </div>

          </div>
        )}

        {/* Edit Course Modal */}
        <EditCourseModal
          isOpen={isEditModalOpen}
          onClose={() => setIsEditModalOpen(false)}
          course={course}
          onSuccess={() => {
            console.log('Course updated successfully');
            // Optionally refresh course data
          }}
        />

        {/* Add Module Button */}
        <div className="flex justify-end mt-4">
          <button
            onClick={handleaddModule}
            className="flex items-center border border-[#0A5224] text-[#0A5224] px-4 py-2 text-[18px] hover:bg-[#0A5224] hover:text-white rounded"
          >
            <span className="mr-1 text-lg">+</span> Add Module
          </button>
        </div>
      </div>
    </div>
  );
};

export default ViewCoursePage;
