// api-services/courses/courses.ts
import { useMutation, useQuery } from "@tanstack/react-query";
import { makeRequest } from "../utils";

// ================== COURSE INTERFACES ==================
interface CreateCoursePayload {
  name: string;
  price: string;
  tags?: string[];
  thumbnail?: File;
}

interface CreateCourseResponse {
  message: string;
  data: {
    id: string;
    name: string;
    tags: string[];
    thumbnail: string;
    price: string;
    is_active: boolean;
    is_published: boolean;
    created_by: {
      id: number;
      name: string;
      email: string;
    };
    created_at: string;
  };
}

// ================== CREATE COURSE API ==================
async function createCourse(payload: CreateCoursePayload): Promise<CreateCourseResponse> {
  // Create FormData for file upload
  const formData = new FormData();

  formData.append("name", payload.name);
  formData.append("price", payload.price);

  // Handle tags - API expects valid JSON
  if (payload.tags && payload.tags.length > 0) {
    // Send as JSON array string
    formData.append("tags", JSON.stringify(payload.tags));
  }

  // Set default values for new course creation
  formData.append("is_active", "true");
  formData.append("is_published", "true");

  // Handle thumbnail upload - API expects URL, not file
  // For now, skip thumbnail upload until we implement file upload endpoint
  // if (payload.thumbnail) {
  //   formData.append("thumbnail", payload.thumbnail);
  // }

  // Debug: Log FormData contents
  console.log("FormData contents:");
  for (let [key, value] of formData.entries()) {
    console.log(`${key}:`, value);
  }

  const response = await makeRequest({
    endpoint: "/api/courses/",
    method: "POST",
    data: formData,
    isFileUpload: true,
  });

  return response;
}

// ================== GET COURSES API ==================
interface GetCoursesResponse {
  message: string;
  data: {
    id: string;
    name: string;
    tags: string[];
    thumbnail: string;
    price: string;
    is_active: boolean;
    is_published: boolean;
    created_by: {
      id: number;
      name: string;
      email: string;
    };
    created_at: string;
  }[];
}

async function getCourses(): Promise<GetCoursesResponse> {
  const response = await makeRequest({
    endpoint: "/api/courses/",
    method: "GET",
  });

  return response;
}

// ================== UPDATE COURSE API ==================
interface UpdateCoursePayload {
  name?: string;
  price?: string;
  tags?: string[];
  thumbnail?: File;
  is_active?: boolean;
  is_published?: boolean;
}

interface UpdateCourseResponse {
  message: string;
  data: {
    id: string;
    name: string;
    tags: string[];
    thumbnail: string;
    price: string;
    is_active: boolean;
    is_published: boolean;
    created_by: {
      id: number;
      name: string;
      email: string;
    };
    created_at: string;
  };
}

async function updateCourse(courseId: string, payload: UpdateCoursePayload): Promise<UpdateCourseResponse> {
  console.log("Calling UPDATE API for course:", courseId, "with payload:", payload);

  // Check if we have file upload or just data update
  const hasFileUpload = payload.thumbnail instanceof File;

  if (hasFileUpload) {
    // Use FormData for file uploads
    const formData = new FormData();

    if (payload.name) formData.append("name", payload.name);
    if (payload.price) formData.append("price", payload.price);
    if (payload.tags && payload.tags.length > 0) {
      formData.append("tags", JSON.stringify(payload.tags));
    }
    if (payload.thumbnail) formData.append("thumbnail", payload.thumbnail);
    if (payload.is_active !== undefined) formData.append("is_active", payload.is_active.toString());
    if (payload.is_published !== undefined) formData.append("is_published", payload.is_published.toString());

    const response = await makeRequest({
      endpoint: `/api/courses/${courseId}/`,
      method: "PUT",
      data: formData,
      isFileUpload: true,
    });

    console.log("UPDATE API response:", response);
    return response;
  } else {
    // Use JSON for regular data updates
    const response = await makeRequest({
      endpoint: `/api/courses/${courseId}/`,
      method: "PUT",
      data: payload as Record<string, unknown>,
    });

    console.log("UPDATE API response:", response);
    return response;
  }
}

// ================== DELETE COURSE API (SOFT DELETE) ==================
interface DeleteCourseResponse {
  message: string;
}

async function deleteCourse(courseId: string): Promise<DeleteCourseResponse> {
  console.log("Calling DELETE API for course:", courseId);

  // Note: Backend DELETE API might already implement soft delete
  // by setting is_active and is_published to false instead of permanent deletion
  const response = await makeRequest({
    endpoint: `/api/courses/${courseId}/`,
    method: "DELETE",
  });

  console.log("DELETE API response:", response);
  return response;
}

// ================== REACT QUERY HOOKS ==================
const useCreateCourse = () => {
  return useMutation<CreateCourseResponse, Error, CreateCoursePayload>({
    mutationFn: createCourse,
    onError: (error) => {
      console.error("Course creation error:", error);
    },
  });
};

const useGetCourses = () => {
  return useQuery<GetCoursesResponse, Error>({
    queryKey: ["courses"],
    queryFn: getCourses,
  });
};

const useUpdateCourse = () => {
  return useMutation<UpdateCourseResponse, Error, { courseId: string; payload: UpdateCoursePayload }>({
    mutationFn: ({ courseId, payload }) => updateCourse(courseId, payload),
  });
};

const useDeleteCourse = () => {
  return useMutation<DeleteCourseResponse, Error, string>({
    mutationFn: deleteCourse,
  });
};

export { useCreateCourse, useGetCourses, useUpdateCourse, useDeleteCourse };
export type { CreateCoursePayload, CreateCourseResponse, GetCoursesResponse, UpdateCoursePayload, UpdateCourseResponse, DeleteCourseResponse };
