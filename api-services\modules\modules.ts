import { makeRequest } from "../utils";
import { useQuery, useMutation } from "@tanstack/react-query";
import { uploadModuleFile } from "../cloudflare/cloudflare";

// ================== MODULE INTERFACES ==================
interface Module {
  id: string;
  title: string;
  overview_md: string;
  course: string;
  created_by: number;
  created_at: string;
}

interface GetModulesResponse {
  message: string;
  data: Module[];
}

interface CreateModulePayload {
  title: string;
  overview_md?: string;
}

interface CreateModuleResponse {
  message: string;
  data: Module;
}

interface UpdateModulePayload {
  title?: string;
  overview_md?: string;
}

interface UpdateModuleResponse {
  message: string;
  data: Module;
}

interface DeleteModuleResponse {
  message: string;
}

// ================== GET MODULES API ==================
async function getModules(courseId: string): Promise<GetModulesResponse> {
  console.log("Calling GET modules API for course:", courseId);

  const response = await makeRequest({
    endpoint: `/api/courses/${courseId}/modules/`,
    method: "GET",
  });

  console.log("GET modules API response:", response);
  return response;
}

// ================== CREATE MODULE API ==================
async function createModule(
  courseId: string,
  payload: CreateModulePayload | FormData
): Promise<CreateModuleResponse> {
  console.log(
    "Calling CREATE module API for course:",
    courseId,
    "with payload:",
    payload
  );

  try {
    const isFormData = payload instanceof FormData;
    let finalPayload: any;

    if (isFormData) {
      // Handle FormData - extract file and upload to Cloudflare
      const formData = payload as FormData;
      const overviewFile = formData.get("overview_md") as File;
      const title = formData.get("title") as string;

      if (overviewFile && overviewFile instanceof File) {
        console.log("Uploading module file to Cloudflare...");
        const uploadResult = await uploadModuleFile(overviewFile);

        if (uploadResult.success) {
          console.log("Module file uploaded successfully:", uploadResult.url);
          // Create JSON payload with Cloudflare URL
          finalPayload = {
            title: title,
            overview_md: uploadResult.url,
          };
        } else {
          throw new Error("Failed to upload module file to Cloudflare");
        }
      } else {
        // No file, just use title
        finalPayload = { title: title };
      }
    } else {
      // Regular JSON payload
      finalPayload = payload as unknown as Record<string, unknown>;
    }

    const response = await makeRequest({
      endpoint: `/api/courses/${courseId}/modules/`,
      method: "POST",
      data: finalPayload,
      isFileUpload: false, // Always send as JSON now
    });

    console.log("CREATE module API response:", response);
    return response;
  } catch (error) {
    console.error("CREATE module API error:", error);
    console.error("Payload that failed:", payload);
    console.error("CourseId:", courseId);
    throw error;
  }
}

// ================== UPDATE MODULE API ==================
async function updateModule(
  courseId: string,
  moduleId: string,
  payload: UpdateModulePayload
): Promise<UpdateModuleResponse> {
  console.log(
    "Calling UPDATE module API for course:",
    courseId,
    "module:",
    moduleId,
    "with payload:",
    payload
  );

  const response = await makeRequest({
    endpoint: `/api/courses/${courseId}/modules/${moduleId}/`,
    method: "PUT",
    data: payload as Record<string, unknown>,
  });

  console.log("UPDATE module API response:", response);
  return response;
}

// ================== DELETE MODULE API ==================
async function deleteModule(
  courseId: string,
  moduleId: string
): Promise<DeleteModuleResponse> {
  console.log(
    "Calling DELETE module API for course:",
    courseId,
    "module:",
    moduleId
  );

  const response = await makeRequest({
    endpoint: `/api/courses/${courseId}/modules/${moduleId}/`,
    method: "DELETE",
  });

  console.log("DELETE module API response:", response);
  return response;
}

// ================== REACT QUERY HOOKS ==================
const useGetModules = (courseId: string) => {
  return useQuery<GetModulesResponse, Error>({
    queryKey: ["modules", courseId],
    queryFn: () => getModules(courseId),
    enabled: !!courseId, // Only run query if courseId exists
  });
};

const useCreateModule = () => {
  return useMutation<
    CreateModuleResponse,
    Error,
    { courseId: string; payload: CreateModulePayload | FormData }
  >({
    mutationFn: ({ courseId, payload }) => createModule(courseId, payload),
  });
};

const useUpdateModule = () => {
  return useMutation<
    UpdateModuleResponse,
    Error,
    { courseId: string; moduleId: string; payload: UpdateModulePayload }
  >({
    mutationFn: ({ courseId, moduleId, payload }) =>
      updateModule(courseId, moduleId, payload),
  });
};

const useDeleteModule = () => {
  return useMutation<
    DeleteModuleResponse,
    Error,
    { courseId: string; moduleId: string }
  >({
    mutationFn: ({ courseId, moduleId }) => deleteModule(courseId, moduleId),
  });
};

export { useGetModules, useCreateModule, useUpdateModule, useDeleteModule };

export type {
  Module,
  GetModulesResponse,
  CreateModulePayload,
  CreateModuleResponse,
  UpdateModulePayload,
  UpdateModuleResponse,
  DeleteModuleResponse,
};
