"use client";
import React, { useState, useEffect } from 'react';
import Image from 'next/image';
import InputField from '@/components/common/adminCommon/InputField';
import { Eye, EyeOff } from "lucide-react";

const ResetForm: React.FC = () => {
  const [formData, setFormData] = useState({
    email: '',
    otp: '',
    password: '',
    confirmPassword: '',
  });

  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [isFormValid, setIsFormValid] = useState(false);

  // Validation regexes
  const emailRegex = React.useMemo(() => /^[^\s@]+@[^\s@]+\.[^\s@]+$/, []);
  const strongPasswordRegex = React.useMemo(() => /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[\W_]).{8,}$/, []);

  // Validate form
  useEffect(() => {
    const { email, password, confirmPassword } = formData;
    const isValid =
      emailRegex.test(email.trim()) &&
      strongPasswordRegex.test(password) &&
      password === confirmPassword;

    setIsFormValid(isValid);
  }, [formData, emailRegex, strongPasswordRegex]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    console.log("Form Data Submitted:", formData);
    // Add backend call or OTP verification here
  };

  return (
    <div className="min-h-screen flex flex-col items-center justify-center bg-white px-4 py-8">
      <div className="bg-[#eeeeee] rounded-md shadow-md pt-8 lg:w-[914px] md:w-[600px] w-full">
        <div className="flex justify-center">
          <Image
            src="/assets/images/logobig.png"
            alt="logo"
            width={130}
            height={60}
            className="h-[55px] w-auto"
          />
        </div>

        <div className='px-15'>
        <p className=" text-center justify-center  font-[Poppins] font-semibold text-[20px] md:text-[32px] leading-[110%] tracking-[-0.01em]  text-[#4b207a] mb-1">
        Reset Password
        </p>
        </div>

        <form onSubmit={handleSubmit} className="space-y-4 w-full flex flex-col items-center py-5">
          <div className="w-full md:w-[75%]">
            <InputField
              id="email"
              name="email"
              type="email"
              value={formData.email}
              onChange={handleChange}
              label="Enter Email"
            />
          </div>

          <div className="w-full md:w-[75%]">
            <InputField
              id="otp"
              name="otp"
              type="text"
              value={formData.otp}
              onChange={handleChange}
              label="Enter OTP"
            />
          </div>

          <div className="w-full md:w-[75%] relative">
            <InputField
              id="password"
              name="password"
              type={showPassword ? 'text' : 'password'}
              value={formData.password}
              onChange={handleChange}
              label="Password"
            />
            <button
              type="button"
              className="absolute right-3 top-1/2 transform -translate-y-1/2 text-[#4b207a]"
              onClick={() => setShowPassword(!showPassword)}
            >
              {showPassword ? <EyeOff size={22} /> : <Eye size={22} />}
            </button>
            {!strongPasswordRegex.test(formData.password) && formData.password !== '' && (
              <p className="text-red-600 text-sm mt-1">
                Must be 8+ characters, include uppercase, lowercase, number, and special character
              </p>
            )}
          </div>

          <div className="w-full md:w-[75%] relative">
            <InputField
              id="confirmPassword"
              name="confirmPassword"
              type={showConfirmPassword ? 'text' : 'password'}
              value={formData.confirmPassword}
              onChange={handleChange}
              label="Confirm Password"
            />
            <button
              type="button"
              className="absolute right-3 top-1/2 transform -translate-y-1/2 text-[#4b207a]"
              onClick={() => setShowConfirmPassword(!showConfirmPassword)}
            >
              {showConfirmPassword ? <EyeOff size={22} /> : <Eye size={22} />}
            </button>
            {formData.confirmPassword !== formData.password && formData.confirmPassword !== '' && (
              <p className="text-red-600 text-sm mt-1">Passwords do not match</p>
            )}
          </div>

          <div className="w-full flex items-center justify-center">
            <button
              type="submit"
              disabled={!isFormValid}
              className={`py-3 px-10 rounded-lg font-bold text-[24px] text-white font-[Poppins] ${
                isFormValid
                  ? 'bg-[#4b207a] opacity-100 cursor-pointer'
                  : 'bg-[#4b207a] opacity-50 cursor-not-allowed'
              }`}
            >
              Reset Password
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default ResetForm;
