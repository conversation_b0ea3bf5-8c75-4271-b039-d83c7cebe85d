const headers = {
  "Content-Type": "application/json",
};

const baseURL = process.env.NEXT_PUBLIC_BASE_URL;

async function makeRequest({
  endpoint,
  method,
  data,
  isFileUpload = false,
   headers: customHeaders = {},
}: {
  endpoint: string;
  method: "GET" | "POST" | "PUT" | "DELETE" | "PATCH";
  data?: Record<string, unknown> | FormData;
  isFileUpload?: boolean;
  headers?: Record<string, string>; //added by punam
}) {
  try {
    const token = localStorage.getItem("token");

    const requestHeaders = {
      ...(isFileUpload ? {} : headers),              // base headers
  ...(token && { Authorization: `Bearer ${token}` }), // token if available
  ...customHeaders,
    };

    const options: RequestInit = {
      method,
      headers: requestHeaders,
      credentials: "include",
      body: data
        ? isFileUpload
          ? (data as FormData)
          : JSON.stringify(data)
        : undefined,
    };

    const resp = await fetch(`${baseURL}${endpoint}`, options);

    if (resp.status === 401) {
      localStorage.removeItem("token");
      await refreshToken();
    }

    if (!resp.ok) {
      let errorDetails;
      try {
        errorDetails = await resp.json();

        // Handle specific error cases without excessive console logging
        if (resp.status === 409) {
          // Course already exists error - don't log to console, just throw clean error
          const errorMessage = errorDetails.message || errorDetails.detail || 'Resource already exists';
          throw new Error(errorMessage);
        }

        // Handle other client errors (400-499) with minimal logging
        if (resp.status >= 400 && resp.status < 500) {
          const errorMessage = errorDetails.message || errorDetails.detail || `Client error: ${resp.status}`;
          throw new Error(errorMessage);
        }

        // Log server errors (500+) to console for debugging
        if (resp.status >= 500) {
          console.error(`Server error ${resp.status}:`, errorDetails);
        }

      } catch (parseError) {
        // If it's already our custom error, re-throw it
        if (parseError instanceof Error && parseError.message !== 'Unexpected end of JSON input') {
          throw parseError;
        }

        // If JSON parsing fails, try to get text
        try {
          errorDetails = await resp.text();
          if (resp.status >= 500) {
            console.error(`HTTP ${resp.status} error:`, errorDetails);
          }
        } catch {
          errorDetails = 'Unknown error occurred';
          if (resp.status >= 500) {
            console.error(`HTTP ${resp.status} error: Unable to parse response`);
          }
        }
      }

      // Create appropriate error message based on status
      let errorMessage = 'An error occurred';
      if (errorDetails?.message) {
        errorMessage = errorDetails.message;
      } else if (errorDetails?.detail) {
        errorMessage = errorDetails.detail;
      } else if (typeof errorDetails === 'string') {
        errorMessage = errorDetails;
      }

      throw new Error(errorMessage);
    }

    return await resp.json();
  } catch (error) {
    console.log("Error", error);
    throw error; // Re-throw the error so mutations can catch it
  }
}

async function refreshToken() {
  const refreshToken = localStorage.getItem("refresh_token");

  if (!refreshToken) {
    console.warn("No refresh token found, redirecting to login");
    localStorage.clear();
    window.location.href = "/Admin/login";
    return;
  }

  try {
    // Direct fetch call to avoid infinite recursion
    const response = await fetch(`${baseURL}/accounts/token/refresh/`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      credentials: "include",
      body: JSON.stringify({
        refresh: refreshToken,
      }),
    });

    if (response.ok) {
      const data = await response.json();
      if (data?.access) {
        localStorage.setItem("token", data.access);
        console.log("Token refreshed successfully");
      } else {
        throw new Error("Invalid refresh response");
      }
    } else {
      throw new Error(`Refresh failed: ${response.status}`);
    }
  } catch (error) {
    console.error("Token refresh failed:", error);
    localStorage.clear();
    window.location.href = "/Admin/login";
  }
}

const QueryKeys = {
  FORM_FIELDS: (id: string) => ["form-fields", id],
  FORM_ELEMENTS: ["form-elements"],
  // USER_FORMS: ["user-forms"],
  USER_PROFILE: ["userProfile"],
  USER_FORMS: (limit: number, offset: number) => ["user-forms", limit, offset],
  UPDATE_FORM: (id: string) => ["update-form", id],
  INTEGRATIONS: ["integrations"],
  FORM_DETAILS: (id: string) => ["form-details", id],
  FORM_PUBLIC: (id: string) => ["form-public", id],
  FORM_CACHE_UPDATE: (id: string) => ["form-cache-update", id],
  GOOGLE_SHEET_AUTH:(id:string,type:string) => ["google-sheet-auth", id,type],
  USER_SHEETS: ["user-sheets"],
  CREATE_SHEET: ["create-sheet"],
};

export { makeRequest, QueryKeys };

import { type ClassValue, clsx } from "clsx"
import { twMerge } from "tailwind-merge"

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}
