"use client";

import React, { useState } from "react";
import Image from "next/image";
import WelcomeContent from "@/components/signupui/welcome";
import { X, <PERSON>, EyeOff, Check } from "lucide-react";

interface CreateNewPasswordData {
  password: string;
  confirmPassword: string;
}

interface ValidationMessages {
  email?: string;
  password?: string;
  confirmPassword?: string;
}

const calculatePasswordStrength = (password: string): {
  strength: number;
  text: string;
  color: string;
} => {
  let strength = 0;
  const checks = {
    length: password.length >= 8,
    hasNumber: /\d/.test(password),
    hasLowerCase: /[a-z]/.test(password),
    hasUpperCase: /[A-Z]/.test(password),
    hasSpecial: /[!@#$%^&*(),.?":{}|<>]/.test(password),
  };

  strength += checks.length ? 20 : 0;
  strength += checks.hasNumber ? 20 : 0;
  strength += checks.hasLowerCase ? 20 : 0;
  strength += checks.hasUpperCase ? 20 : 0;
  strength += checks.hasSpecial ? 20 : 0;

  if (strength <= 20) return { strength, text: "Very Weak", color: "bg-red-500" };
  if (strength <= 40) return { strength, text: "Weak", color: "bg-orange-500" };
  if (strength <= 60) return { strength, text: "Good", color: "bg-yellow-500" };
  if (strength <= 80) return { strength, text: "Strong", color: "bg-blue-500" };
  return { strength, text: "Very Strong", color: "bg-green-500" };
};

const ForgotPassword: React.FC = () => {
  const [step, setStep] = useState(1);
  const [email, setEmail] = useState("");
 
  const [formData, setFormData] = useState<CreateNewPasswordData>({
    password: "",
    confirmPassword: "",
  });
  const [, setValidationMessages] = useState<ValidationMessages>({});
  const [isLoading, setIsLoading] = useState(false);
  const [showPassword, setShowPassword] = useState(false);


  const passwordStrength = calculatePasswordStrength(formData.password);

  const validateEmail = () => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    const messages: ValidationMessages = {};

    if (!email) {
      messages.email = "Email is required";
    } else if (!emailRegex.test(email)) {
      messages.email = "Please enter a valid email address";
    }

    setValidationMessages(messages);
    return Object.keys(messages).length === 0;
  };

  const handleNext = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (step === 1 && validateEmail()) {
      setStep(2);
    } else if (step === 2) {
      setStep(3);
    }
  };

  const validateStep3 = () => {
    const messages: ValidationMessages = {};
    let isValid = true;

    if (!formData.password) {
      messages.password = "Password is required";
      isValid = false;
    } else if (formData.password.length < 8) {
      messages.password = "Password must be at least 8 characters long";
      isValid = false;
    }

    if (formData.password !== formData.confirmPassword) {
      messages.confirmPassword = "Passwords do not match";
      isValid = false;
    }

    setValidationMessages(messages);
    return isValid;
  };

  const handleResetPassword = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (validateStep3()) {
      setIsLoading(true);
      try {
        // Simulate API call
        await new Promise(resolve => setTimeout(resolve, 1500));
        setIsLoading(false);
        setStep(4);
      } catch (error) {
        console.error("Error resetting password:", error);
        setIsLoading(false);
      }
    }
  };

  const handleSignIn = () => {
    window.location.href = "/login";
  };

  const handleClose = (e: React.MouseEvent) => {
    e.preventDefault();
    // Navigate back to the previous page
    window.history.back();
  };

  function handleSubmit(event: React.FormEvent<HTMLFormElement>): void {
    event.preventDefault();
    if (validateEmail()) {
      setStep(2); // Move to next step
    }
  }

  return (
    <>
      <header className="border-b border-[#868686] flex flex-row items-center justify-between">
        <div>
          {/* Header with logo */}
          <div className="p-1">
            <Image
              src="/assets/images/logobig.png"
              alt="logo"
              width={395}
              height={128}
              className="w-[80px] sm:w-[100px] lg:w-[130px] p-1 ml-0 sm:ml-4 md:ml-[60px] mb-2"
            />
          </div>
        </div>
        <div className="flex items-center justify-between py-1 md:py-2 lg:py-2">
          
        </div>
      </header>
      <section className="flex flex-col">
        <div className="px-4 sm:px-3 xl:px-[120px] flex flex-col">
          {/* Main content */}
          <div className="flex flex-col md:flex-row items-center justify-between py-6 sm:py-8 md:py-10 lg:py-12 xl:py-16">
            {/* Login Form - LEFT SIDE - Make sure this comes first in the flex container */}
            <div className="bg-[#EBEBEB] p-4 sm:p-6 lg:p-8 xl:p-10 rounded-lg shadow-[4px_4px_4px_0px_rgba(0,0,0,0.25)] w-full md:w-1/2 max-w-[95%] sm:max-w-[500px] md:max-w-[450px] lg:max-w-[500px] xl:max-w-[600px] relative mb-6 md:mb-0">
              {/* Close button */}
              <button
                className="absolute top-3 right-3 md:top-4 md:right-4 text-gray-500 hover:text-gray-700 font-bold text-xl"
                onClick={handleClose}
              >
                <X size={24} />
              </button>

              <h2 className="font-poppins text-left text-[24px] sm:text-[28px] md:text-[32px] xl:text-[36px] font-[600] mb-2 font-poppins text-[#4B207A]">
                Forgot Password{" "}
                <span className="text-[#0A5224] text-xs sm:text-sm xl:text-base font-poppins">
                  EduTech
                </span>
              </h2>
              
              {(step === 1 || step === 2) && (
                <p className="text-slate-600 text-xs sm:text-sm mt-2 font-poppins">
                  Enter Your Email Address...we will send the link to your Email
                </p>
              )}
              
          
              {/* Email Form - Step 1 */}
              {step === 1 && (
                <form onSubmit={handleSubmit} className="mt-6 space-y-4">
                  {/* Email Field */}
                  <div className="relative w-full">
  <input
    type="email"
    id="email"
    placeholder="Enter Your Email"
    value={email}
    onChange={(e) => setEmail(e.target.value)}
    className="peer w-full px-3 pt-6 pb-2 border border-[#000000] rounded focus:outline-none focus:ring-2 focus:ring-blue-500 placeholder-transparent"
    required
  />
  <label
    htmlFor="email"
    className="absolute left-3 bottom-[34px] text-[#0065ff] text-sm transition-all duration-200
    peer-placeholder-shown:bottom-[20px] peer-placeholder-shown:text-base
    peer-placeholder-shown:text-gray-500
    peer-focus:bottom-[35px] peer-focus:text-sm peer-focus:text-[#0065ff] text-[12px] font-[400] font-poppins leading-[150%] space-x-3"
>
    Email
  </label>
</div>

                
                  {/* Submit Button */}
                  <button
                    type="submit"
                    className="w-full bg-[#0D47A1] text-white font-medium py-3 rounded-md transition-colors font-poppins mt-5 cursor-pointer"
                  >
                    Send Reset Link
                  </button>
                </form>
              )}

              {/* Email Confirmation - Step 2 */}
              {step === 2 && (
                <div className=" h-full">
                 

                 
                  <form className="space-y-5 mt-2">
                    <div className="space-y-2">
                      <div className="relative">
                        <input
                          type="email"
                          placeholder="Enter your email address"
                          value={email}
                          disabled
                          className="w-full p-3 border rounded-lg focus:outline-none focus:ring-2 focus:ring-[#006D8F] pr-10 bg-gray-100"
                        />
                      </div>
                      <button
                        type="button"
                        className="w-full bg-[#063585] text-white py-3 px-4 rounded-md  transition-colors duration-200 mt-4 cursor-pointer"
                        onClick={handleNext}
                        disabled={isLoading}
                      >
                        {isLoading ? "Sending..." : "Continue"}
                      </button>
                      <div className="mb-4">
                        <p className="text-[#083747] text-sm font-poppins">
                          We&apos;ve sent a password reset link to your email address:{' '}
                          <a href={`mailto:${email}`} className="text-blue-600">
                            {email} <span className="text-[#083747] text-sm font-poppins">Click the link in your email to create a new password. </span> 
                          </a>
                        </p>
                       
                        <p className="text-gray-600 mt-2 text-sm font-poppins">
                          If you don&apos;t see the email in your inbox, please check your spam or junk folder.
                        </p>
                      </div>
                    </div>
                  </form>
                </div>
              )}

              {/* Reset Password Form - Step 3 */}
              {step === 3 && (
                
                <form onSubmit={handleResetPassword} className="space-y-4">
                  {/* Password Field */}
                 
                  <p className="text-gray-600 text-[10px] md:text-[12px] mt-2 font-poppins">Set Your New Password</p>
                  <p className="text-gray-600 mb-4 text-[10px] md:text-[12px] font-poppins mt-[-10]">Please enter and confirm your new password below to reset your account.</p>
                  <div className="relative w-full">
                    <input
                      type={showPassword ? "text" : "password"}
                      placeholder=" "
                      value={formData.password}
                      onChange={(e) => setFormData({ ...formData, password: e.target.value })}
                      className="peer w-full px-3 pt-5 pb-2 border border-black rounded-md focus:outline-none focus:ring-2 focus:ring-[#0065ff] mb-[-45px] "
                      required
                    />
                    <label
                    className="absolute left-3 bottom-[23px] text-[#0065ff] text-sm transition-all duration-200
                    peer-placeholder-shown:top-[15px] peer-placeholder-shown:text-base
              peer-placeholder-shown:text-gray-500
              peer-focus:bottom-[24px] peer-focus:text-sm peer-focus:text-[#0065ff] text-[12px] font-[400] font-poppins leading-[150%] space-x-3"
          >
                      Enter your new password
                    </label>
                    <button
                      type="button"
                      onClick={() => setShowPassword(!showPassword)}
                      className="absolute right-3 top-3 text-gray-500"
                    >
                      {showPassword ? <EyeOff size={20} /> : <Eye size={20} />}
                    </button>
                  </div>

                  {/* Password Strength Bar */}
                  <div className="w-full h-2 rounded mt-2 ">
                    <div className={`h-full ${passwordStrength.color}`} style={{ width: `${passwordStrength.strength}%` }} />
                    <p className="text-xs mt-1 text-gray-700 font-medium">{passwordStrength.text}</p>
                  </div>

                  {/* Confirm Password Field */}
                  <div className="relative w-full ">
                    <input
                      type="password"
                      placeholder=" "
                      value={formData.confirmPassword}
                      onChange={(e) =>
                        setFormData({ ...formData, confirmPassword: e.target.value })
                      }
                      className="peer w-full px-3 mt-5 pt-5 pb-2 border border-black rounded-md focus:outline-none focus:ring-2 focus:ring-[#0065ff] mb-[-45px] "
                      required
                    />
                    <label
                   className="absolute left-3 bottom-[23px] text-[#0065ff] text-sm  transition-all duration-200
                   peer-placeholder-shown:top-[35px] peer-placeholder-shown:text-base
             peer-placeholder-shown:text-gray-500
             peer-focus:bottom-[24px] peer-focus:text-sm peer-focus:text-[#0065ff] text-[12px] font-[400] font-poppins leading-[150%] space-x-3"
         >
                     Re Enter Your Password
                    </label>
                  </div>

                  <button
                    type="submit"
                    className="w-full bg-[#0D47A1] text-white font-medium py-3 rounded transition-colors mt-4 cursor-pointer"
                  >
                    {isLoading ? "Resetting..." : "Reset Password"}
                  </button>
                </form>
              )}

{step === 4 && (
            <div className="p-4 sm:p-6 md:p-8 h-full">
             
              <div className="space-y-4 mt-4">
                <div className="flex flex-col items-center justify-center">
                  <div className="relative w-16 h-16 mb-6">
                    <div className="absolute inset-0 bg-green-900 rounded-full flex items-center justify-center">
                      <Check 
                        className="w-8 h-8 text-[#1cfe03]" 
                        strokeWidth={3}
                      />
                    </div>
                  </div>

                  <h2 className="text-[#0A5224] text-base font-semibold font-poppins mb-8 text-center font-poppins">
                    Password Changed Successfully!
                  </h2>
              
                  <button 
                    className="w-full bg-[#063585] text-white py-2 px-5 rounded-lg font-semibold font-poppins"
                    onClick={handleSignIn}
                  >
                    Login
                  </button>
                </div>
              </div>
            </div>
          )}
        </div>


            {/* WelcomeContent - RIGHT SIDE */}
            <div className="w-full md:w-1/2  items-center justify-center md:block hidden">
              <WelcomeContent />
            </div>
          </div>
        </div>
      </section>
    </>
  );
};

export default ForgotPassword;