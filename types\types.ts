

// types.ts
// src/types/learningPaths.ts
export interface LearningPath {
  id: number;
    image: string;
    title: string;
    link: string;
  }
  
  export interface Topic {
    icon: string;
    title: string;
    href: string;
  }
  export interface Tool {
    src: string;
    alt: string;
  }
  export interface Errors {
    name: string;
    email: string;
    phone: string;
    aboutUs: string;
  }

  export interface FormData {
    name: string;
    email: string;
    phone: string;
    aboutUs: string;
  }

  export interface CardProps {
    image: string;
    title: string;
    description: string;
    href?: string;
  }
  export interface Benefit {
    title: string;
    description: string;
  }
  // Define an interface for the faculty member structure
export interface FacultyMember {
  name: string;
  degree: string;
  experience: string;
  teaching: string;
  skills: string;
  image: string;
  linkedin: string;
}
export interface Project {
  title: string;
  description: string;
  image: string;
}

// Define the type for each testimonial
export interface Testimonial {
  text: string;
  author: string;
}

// by ankit
export interface ModuleItem {
  id: number
  title: string
  type: "lesson" | "reading" | "assignment" | "book"
  completed?: boolean
  active?: boolean
}

export interface Module {
  id: string
  title: string
  intro?: string
  items: ModuleItem[]
}
export interface CourseCardProps {
  course: {
    id: number
    title: string
    price: number
    image: string
    isPaid: boolean
    badge?: string
    badgeColor?: string
    badgeImage?: string
    
  }
}


// about page
export interface StatItem {
  number: string;
  label: string;
}
export type AboutTestimonial = {
  name: string;
  title: string;
  rating: number;
  feedback: string;
  image: string;
};

// quiz

export interface QuizQuestion {
  id: number
  question: string
  options: string[]
  correctAnswer: number
  explanation: string
}

